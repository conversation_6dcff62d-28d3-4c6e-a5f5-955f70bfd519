<?php
/**
 * Web-Based Email Removal Migration
 * Blood Donation Management System
 * 
 * This script runs the migration to remove email functionality from the system.
 * Access via browser: http://localhost/SaLin/web_migration.php
 */

require_once 'config/database.php';
require_once 'config/constants.php';

$message = '';
$error = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_migration'])) {
    try {
        $db = Database::getInstance();
        $db->beginTransaction();
        
        $steps = [];
        
        // Step 1: Remove email column from users table
        $columns = $db->fetchAll("SHOW COLUMNS FROM users LIKE 'email'");
        if (!empty($columns)) {
            $db->execute("ALTER TABLE users DROP COLUMN email");
            $steps[] = "✓ Email column removed from users table";
        } else {
            $steps[] = "✓ Email column already removed from users table";
        }
        
        // Step 2: Remove email_verified column from users table
        $columns = $db->fetchAll("SHOW COLUMNS FROM users LIKE 'email_verified'");
        if (!empty($columns)) {
            $db->execute("ALTER TABLE users DROP COLUMN email_verified");
            $steps[] = "✓ Email verified column removed from users table";
        } else {
            $steps[] = "✓ Email verified column already removed from users table";
        }
        
        // Step 3: Update user registration source
        $db->execute("UPDATE users SET registration_source = 'unified' WHERE registration_source IS NULL OR registration_source = ''");
        $steps[] = "✓ User registration sources updated";
        
        // Step 4: Remove email-related indexes
        $indexes = $db->fetchAll("SHOW INDEX FROM users WHERE Key_name = 'idx_email'");
        if (!empty($indexes)) {
            $db->execute("DROP INDEX idx_email ON users");
            $steps[] = "✓ Email index removed";
        } else {
            $steps[] = "✓ Email index already removed";
        }
        
        // Step 5: Update password reset table
        $columns = $db->fetchAll("SHOW COLUMNS FROM password_resets LIKE 'email'");
        if (!empty($columns)) {
            $db->execute("ALTER TABLE password_resets DROP COLUMN email");
            $steps[] = "✓ Email column removed from password_resets table";
        } else {
            $steps[] = "✓ Email column already removed from password_resets table";
        }
        
        // Step 6: Remove email-related system settings
        $tables = $db->fetchAll("SHOW TABLES LIKE 'system_settings'");
        if (!empty($tables)) {
            $db->execute("DELETE FROM system_settings WHERE setting_key = 'require_email_verification'");
            $db->execute("DELETE FROM system_settings WHERE setting_key = 'smtp_host'");
            $db->execute("DELETE FROM system_settings WHERE setting_key = 'smtp_port'");
            $db->execute("DELETE FROM system_settings WHERE setting_key = 'smtp_username'");
            $db->execute("DELETE FROM system_settings WHERE setting_key = 'smtp_password'");
            $db->execute("DELETE FROM system_settings WHERE setting_key = 'from_email'");
            $db->execute("DELETE FROM system_settings WHERE setting_key = 'from_name'");
            $steps[] = "✓ Email-related system settings removed";
        } else {
            $steps[] = "✓ System settings table doesn't exist - skipping";
        }
        
        // Step 7: Clean user profiles
        $db->execute("UPDATE users SET profile_photo = NULL WHERE profile_photo LIKE '%@%'");
        $steps[] = "✓ User profile email references cleaned";
        
        $db->commit();
        
        $success = true;
        $message = "Migration completed successfully! Email functionality has been completely removed from the system.";
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "Migration failed: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Removal Migration - Blood Donation System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .migration-card { max-width: 800px; }
        .step-item { margin-bottom: 10px; }
        .warning-box { background-color: #fff3cd; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card migration-card shadow">
                    <div class="card-header bg-danger text-white">
                        <h4><i class="fas fa-database"></i> Email Removal Migration</h4>
                        <p class="mb-0">Remove email functionality from the system</p>
                    </div>
                    
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> Migration Successful!</h5>
                                <p><?php echo $message; ?></p>
                                <hr>
                                <h6>Migration Steps Completed:</h6>
                                <ul>
                                    <?php foreach ($steps as $step): ?>
                                        <li class="step-item"><?php echo htmlspecialchars($step); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                                <hr>
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> What Changed:</h6>
                                    <ul class="mb-0">
                                        <li>Users can now only login with their username</li>
                                        <li>Password reset works with username instead of email</li>
                                        <li>Registration no longer requires email</li>
                                        <li>All email-related database columns have been removed</li>
                                    </ul>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="index.php" class="btn btn-primary">
                                        <i class="fas fa-home"></i> Go to Homepage
                                    </a>
                                    <a href="login.php" class="btn btn-success">
                                        <i class="fas fa-sign-in-alt"></i> Test Login
                                    </a>
                                </div>
                            </div>
                        <?php elseif ($error): ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle"></i> Migration Failed</h5>
                                <p><?php echo htmlspecialchars($error); ?></p>
                                <a href="web_migration.php" class="btn btn-warning">
                                    <i class="fas fa-redo"></i> Try Again
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="warning-box p-3 mb-4">
                                <h6><i class="fas fa-exclamation-triangle"></i> Important Warning</h6>
                                <p class="mb-0">
                                    <strong>This migration will permanently remove all email functionality from the system.</strong>
                                    This action cannot be undone. Please ensure you have backed up your database before proceeding.
                                </p>
                            </div>
                            
                            <h5><i class="fas fa-list"></i> What This Migration Will Do:</h5>
                            <ul>
                                <li>Remove email column from users table</li>
                                <li>Remove email_verified column from users table</li>
                                <li>Remove email-related database indexes</li>
                                <li>Update password reset functionality to use username</li>
                                <li>Remove email-related system settings</li>
                                <li>Update user registration sources</li>
                            </ul>
                            
                            <h5><i class="fas fa-info-circle"></i> After Migration:</h5>
                            <ul>
                                <li>Users will only be able to login with their username</li>
                                <li>Password reset will work with username instead of email</li>
                                <li>Registration will not require email</li>
                                <li>All interfaces will be updated to work without email</li>
                            </ul>
                            
                            <form method="POST" class="mt-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="confirm" required>
                                    <label class="form-check-label" for="confirm">
                                        I understand this will permanently remove email functionality and I have backed up my database
                                    </label>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" name="confirm_migration" class="btn btn-danger btn-lg" disabled id="migrateBtn">
                                        <i class="fas fa-database"></i> Run Email Removal Migration
                                    </button>
                                    <a href="index.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Enable/disable migration button based on checkbox
        document.getElementById('confirm').addEventListener('change', function() {
            document.getElementById('migrateBtn').disabled = !this.checked;
        });
    </script>
</body>
</html> 