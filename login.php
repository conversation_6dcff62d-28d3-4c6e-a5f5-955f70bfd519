<?php
/**
 * Login Page
 * Blood Donation Management System
 */

require_once 'config/constants.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';

// Start session
if (!startSecureSession()) {
    // Don't redirect to login.php from login.php - just show error
    $errors[] = ERROR_MESSAGES['SESSION_EXPIRED'] ?? 'Session error occurred';
}

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    switch ($user['user_type']) {
        case USER_TYPE_ADMIN:
            header('Location: admin/');
            exit;
            break;
        case 'unified':
            header('Location: dashboard/');
            exit;
            break;
        case USER_TYPE_DONOR:
            // Legacy donor - redirect to unified dashboard
            header('Location: dashboard/');
            exit;
            break;
        case USER_TYPE_RECIPIENT:
            // Legacy recipient - redirect to unified dashboard
            header('Location: dashboard/');
            exit;
            break;
        default:
            // Unknown user type - logout and show error
            session_destroy();
            $errors[] = 'Invalid user type. Please login again.';
            break;
    }
}

$errors = [];
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Validate input
        $validation = validateLogin($_POST);
        
        if ($validation->isValid) {
            $username = sanitizeInput($_POST['username']);
            $password = $_POST['password'];
            
            // Check rate limiting
            if (!checkLoginAttempts($username)) {
                $errors[] = 'Too many failed login attempts. Please try again later.';
            } else {
                // Attempt authentication
                $user = authenticateUser($username, $password);
                
                if ($user) {
                    // Check if account is active
                    if ($user['status'] !== USER_STATUS_ACTIVE) {
                        $errors[] = ERROR_MESSAGES['ACCOUNT_SUSPENDED'];
                        recordLoginAttempt($username, false);
                    } else {
                        // Login successful
                        loginUser($user);
                        
                        // Update last login only for database users
                        if ($user['id'] > 0) {
                            updateLastLogin($user['id']);
                        }
                        
                        recordLoginAttempt($username, true);
                        
                        // Redirect based on user type
                        switch ($user['user_type']) {
                            case USER_TYPE_ADMIN:
                                header('Location: admin/');
                                exit;
                                break;
                            case 'unified':
                                // Redirect to unified dashboard
                                redirectWithMessage('dashboard/', SUCCESS_MESSAGES['LOGIN_SUCCESS'], 'success');
                                break;
                            case USER_TYPE_DONOR:
                                // Legacy donor - redirect to unified dashboard
                                redirectWithMessage('dashboard/', SUCCESS_MESSAGES['LOGIN_SUCCESS'], 'success');
                                break;
                            case USER_TYPE_RECIPIENT:
                                // Legacy recipient - redirect to unified dashboard
                                redirectWithMessage('dashboard/', SUCCESS_MESSAGES['LOGIN_SUCCESS'], 'success');
                                break;
                            default:
                                redirectWithMessage('login.php', 'Invalid user type', 'error');
                        }
                    }
                } else {
                    $errors[] = ERROR_MESSAGES['INVALID_LOGIN'];
                    recordLoginAttempt($username, false);
                }
            }
        } else {
            $errors = array_merge($errors, array_values($validation->getErrors()));
        }
    }
}

// Get flash message
$flash = getFlashMessage();
if ($flash) {
    if ($flash['type'] === 'success') {
        $success = $flash['message'];
    } else {
        $errors[] = $flash['message'];
    }
}

// Generate CSRF token
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow-sm mt-5">
                    <div class="card-header bg-danger text-white text-center">
                        <h4><i class="fas fa-tint"></i> <?php echo APP_NAME; ?></h4>
                        <p class="mb-0">Login to your account</p>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="login.php">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">Username or Email</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                           required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember">
                                <label class="form-check-label" for="remember">
                                    Remember me
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-sign-in-alt"></i> Login
                                </button>
                            </div>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="forgot-password.php" class="text-decoration-none">
                                <i class="fas fa-key"></i> Forgot Password?
                            </a>
                        </div>
                        
                        <div class="text-center mt-3">
                            <p class="mb-0">Don't have an account?</p>
                            <a href="register.php" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-user-plus"></i> Register Now
                            </a>
                        </div>
                    </div>
                    <div class="card-footer text-center text-muted">
                        <small>
                            <i class="fas fa-shield-alt"></i> Secure Login
                        </small>
                    </div>
                </div>
                

            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
