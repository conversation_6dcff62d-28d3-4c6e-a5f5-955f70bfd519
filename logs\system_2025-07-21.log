[2025-07-21 09:25:14] INFO: Basic user account created Context: {"user_id":"12","username":"EARL"}
[2025-07-21 09:26:05] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"EARL","user_id":12}}
[2025-07-21 09:26:05] INFO: User logged in Context: {"user_id":12,"username":"EARL","user_type":"unified","ip_address":"127.0.0.1"}
[2025-07-21 09:31:27] INFO: User logged out Context: {"user_id":12,"username":"EARL"}
[2025-07-21 09:38:12] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"EARL","user_id":12}}
[2025-07-21 09:38:12] INFO: User logged in Context: {"user_id":12,"username":"EARL","user_type":"unified","ip_address":"127.0.0.1"}
[2025-07-21 10:06:08] INFO: User logged out Context: {"user_id":12,"username":"EARL"}
[2025-07-21 10:12:25] SECURITY: ADMIN_LOGIN_FAILED Context: {"event":"ADMIN_LOGIN_FAILED","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin2"}}
[2025-07-21 10:12:29] SECURITY: ADMIN_LOGIN_FAILED Context: {"event":"ADMIN_LOGIN_FAILED","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin2"}}
[2025-07-21 10:12:48] SECURITY: ADMIN_LOGIN_FAILED Context: {"event":"ADMIN_LOGIN_FAILED","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin2"}}
[2025-07-21 10:13:07] SECURITY: ADMIN_LOGIN_SUCCESS Context: {"event":"ADMIN_LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin2"}}
[2025-07-21 10:13:07] INFO: User logged in Context: {"user_id":10,"username":"admin2","user_type":"admin","ip_address":"127.0.0.1"}
[2025-07-21 10:14:56] ERROR: Database migration failed Context: {"migration":"migrate_to_unified_system","error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL' at line 3"}
[2025-07-21 10:15:58] ERROR: Database migration failed Context: {"migration":"migrate_to_unified_system","error":"There is no active transaction"}
[2025-07-21 10:17:29] INFO: Database migration completed Context: {"migration":"migrate_to_unified_system","statements_executed":2}
[2025-07-21 10:17:52] INFO: Basic user account created Context: {"user_id":"13","username":"unified_test_1753093072"}
[2025-07-21 10:17:52] INFO: Basic user account created Context: {"user_id":"14","username":"role_test_1753093072"}
[2025-07-21 10:19:05] INFO: Basic user account created Context: {"user_id":"15","username":"unified_test_1753093145"}
[2025-07-21 10:19:05] INFO: Basic user account created Context: {"user_id":"16","username":"role_test_1753093145"}
[2025-07-21 10:19:05] INFO: Role added to user Context: {"user_id":16,"role_type":"donor"}
[2025-07-21 10:19:05] INFO: Role added to user Context: {"user_id":16,"role_type":"recipient"}
[2025-07-21 10:19:05] INFO: Basic user account created Context: {"user_id":"17","username":"admin_test_1753093145"}
[2025-07-21 10:19:05] INFO: Role added to user Context: {"user_id":17,"role_type":"donor"}
[2025-07-21 10:19:05] INFO: Role added to user Context: {"user_id":17,"role_type":"recipient"}
[2025-07-21 10:19:45] INFO: Basic user account created Context: {"user_id":"18","username":"unified_test_1753093185"}
[2025-07-21 10:19:45] INFO: Basic user account created Context: {"user_id":"19","username":"role_test_1753093185"}
[2025-07-21 10:19:45] INFO: Role added to user Context: {"user_id":19,"role_type":"donor"}
[2025-07-21 10:19:45] INFO: Role added to user Context: {"user_id":19,"role_type":"recipient"}
[2025-07-21 10:19:45] INFO: Basic user account created Context: {"user_id":"20","username":"admin_test_1753093185"}
[2025-07-21 10:19:45] INFO: Role added to user Context: {"user_id":20,"role_type":"donor"}
[2025-07-21 10:19:45] INFO: Role added to user Context: {"user_id":20,"role_type":"recipient"}
[2025-07-21 10:20:00] INFO: User logged out Context: {"user_id":10,"username":"admin2"}
[2025-07-21 10:21:55] INFO: Basic user account created Context: {"user_id":"21","username":"testuser2025"}
[2025-07-21 10:22:27] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"testuser2025","user_id":21}}
[2025-07-21 10:22:27] INFO: User logged in Context: {"user_id":21,"username":"testuser2025","user_type":"unified","ip_address":"127.0.0.1"}
[2025-07-21 10:24:25] INFO: Role added to user Context: {"user_id":21,"role_type":"donor"}
[2025-07-21 10:25:56] INFO: Role added to user Context: {"user_id":21,"role_type":"recipient"}
[2025-07-21 10:41:34] SECURITY: LOGIN_FAILED_WRONG_PASSWORD Context: {"event":"LOGIN_FAILED_WRONG_PASSWORD","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"testuser","user_id":11}}
[2025-07-21 10:41:42] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"testuser","user_id":11}}
[2025-07-21 10:41:42] INFO: User logged in Context: {"user_id":11,"username":"testuser","user_type":"donor","ip_address":"127.0.0.1"}
[2025-07-21 10:42:34] INFO: User logged out Context: {"user_id":11,"username":"testuser"}
[2025-07-21 10:42:48] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"user3","user_id":8}}
[2025-07-21 10:42:48] INFO: User logged in Context: {"user_id":8,"username":"user3","user_type":"unified","ip_address":"127.0.0.1"}
[2025-07-21 10:44:29] INFO: Role added to user Context: {"user_id":8,"role_type":"recipient"}
[2025-07-21 10:48:23] SECURITY: ADMIN_LOGIN_SUCCESS Context: {"event":"ADMIN_LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin"}}
[2025-07-21 10:48:23] INFO: User logged in Context: {"user_id":1,"username":"admin","user_type":"admin","ip_address":"127.0.0.1"}
[2025-07-21 10:48:32] ERROR: Messaging schema update failed Context: {"error":"SQLSTATE[HY000]: General error: 1193 Unknown system variable 'thread_id_val'"}
[2025-07-21 10:49:00] ERROR: Messaging schema update failed Context: {"error":"Database operation failed"}
[2025-07-21 10:49:47] INFO: Simple messaging schema updated Context: {"executed_statements":3,"skipped_statements":0}
[2025-07-21 10:50:59] INFO: User logged out Context: {"user_id":1,"username":"admin"}
[2025-07-21 10:51:34] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"testuser2025","user_id":21}}
[2025-07-21 10:51:34] INFO: User logged in Context: {"user_id":21,"username":"testuser2025","user_type":"unified","ip_address":"127.0.0.1"}
[2025-07-21 10:59:57] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"testuser2025","user_id":21}}
[2025-07-21 10:59:57] INFO: User logged in Context: {"user_id":21,"username":"testuser2025","user_type":"unified","ip_address":"127.0.0.1"}
[2025-07-21 11:19:58] ERROR: Failed to get profile data Context: {"user_id":21,"error":"Database operation failed"}
[2025-07-21 11:21:07] ERROR: Failed to get profile data Context: {"user_id":21,"error":"Database operation failed"}
[2025-07-21 11:22:12] ERROR: Failed to get profile data Context: {"user_id":21,"error":"Database operation failed"}
[2025-07-21 11:23:32] ERROR: Profile update failed Context: {"user_id":21,"error":"Database operation failed"}
[2025-07-21 11:23:32] ERROR: Failed to get profile data Context: {"user_id":21,"error":"Database operation failed"}
[2025-07-21 11:23:43] INFO: User logged out Context: {"user_id":21,"username":"testuser2025"}
[2025-07-21 11:24:37] SECURITY: ADMIN_LOGIN_SUCCESS Context: {"event":"ADMIN_LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin"}}
[2025-07-21 11:24:37] INFO: User logged in Context: {"user_id":1,"username":"admin","user_type":"admin","ip_address":"127.0.0.1"}
[2025-07-21 11:30:54] INFO: Profile management migration completed Context: {"executed_statements":0,"skipped_statements":0}
[2025-07-21 11:37:21] INFO: User logged out Context: {"user_id":1,"username":"admin"}
[2025-07-21 11:37:52] SECURITY: LOGIN_FAILED_WRONG_PASSWORD Context: {"event":"LOGIN_FAILED_WRONG_PASSWORD","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"testuser2025","user_id":21}}
[2025-07-21 11:38:13] SECURITY: LOGIN_FAILED_WRONG_PASSWORD Context: {"event":"LOGIN_FAILED_WRONG_PASSWORD","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"testuser2025","user_id":21}}
[2025-07-21 11:38:56] SECURITY: ADMIN_LOGIN_SUCCESS Context: {"event":"ADMIN_LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin"}}
[2025-07-21 11:38:56] INFO: User logged in Context: {"user_id":1,"username":"admin","user_type":"admin","ip_address":"127.0.0.1"}
[2025-07-21 11:39:06] ERROR: Failed to get profile data Context: {"user_id":1,"error":"Database operation failed"}
[2025-07-21 11:41:24] ERROR: Failed to get profile data Context: {"user_id":8,"error":"Database operation failed"}
[2025-07-21 11:43:11] ERROR: Failed to get profile data Context: {"user_id":1,"error":"Database operation failed"}
[2025-07-21 11:43:51] INFO: User logged out Context: {"user_id":1,"username":"admin"}
[2025-07-21 11:44:15] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"user3","user_id":8}}
[2025-07-21 11:44:15] INFO: User logged in Context: {"user_id":8,"username":"user3","user_type":"unified","ip_address":"127.0.0.1"}
[2025-07-21 11:44:18] ERROR: Failed to get profile data Context: {"user_id":8,"error":"Database operation failed"}
[2025-07-21 11:44:58] ERROR: Failed to get profile data Context: {"user_id":8,"error":"Database operation failed"}
[2025-07-21 11:45:32] ERROR: Failed to get profile data Context: {"user_id":8,"error":"Database operation failed"}
[2025-07-21 11:45:43] ERROR: Failed to get profile data Context: {"user_id":8,"error":"Database operation failed"}
[2025-07-21 11:45:49] ERROR: Failed to get profile data Context: {"user_id":8,"error":"Database operation failed"}
[2025-07-21 11:57:15] INFO: User logged out Context: {"user_id":8,"username":"user3"}
[2025-07-21 11:57:56] SECURITY: ADMIN_LOGIN_SUCCESS Context: {"event":"ADMIN_LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin"}}
[2025-07-21 11:57:56] INFO: User logged in Context: {"user_id":1,"username":"admin","user_type":"admin","ip_address":"127.0.0.1"}
[2025-07-21 11:59:29] INFO: Notification created Context: {"notification_id":"10","created_by":1,"target_audience":"all"}
[2025-07-21 11:59:52] INFO: User logged out Context: {"user_id":1,"username":"admin"}
[2025-07-21 12:00:27] SECURITY: LOGIN_FAILED_WRONG_PASSWORD Context: {"event":"LOGIN_FAILED_WRONG_PASSWORD","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"user3","user_id":8}}
[2025-07-21 12:00:57] SECURITY: LOGIN_FAILED_WRONG_PASSWORD Context: {"event":"LOGIN_FAILED_WRONG_PASSWORD","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"<EMAIL>","user_id":8}}
[2025-07-21 12:01:24] SECURITY: LOGIN_FAILED_USER_NOT_FOUND Context: {"event":"LOGIN_FAILED_USER_NOT_FOUND","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"<EMAIL>"}}
[2025-07-21 12:01:48] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"user3","user_id":8}}
[2025-07-21 12:01:48] INFO: User logged in Context: {"user_id":8,"username":"user3","user_type":"unified","ip_address":"127.0.0.1"}
[2025-07-21 12:02:51] INFO: User logged out Context: {"user_id":8,"username":"user3"}
[2025-07-21 12:03:25] SECURITY: ADMIN_LOGIN_SUCCESS Context: {"event":"ADMIN_LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"admin"}}
[2025-07-21 12:03:25] INFO: User logged in Context: {"user_id":1,"username":"admin","user_type":"admin","ip_address":"127.0.0.1"}
[2025-07-21 12:04:54] INFO: Notification created Context: {"notification_id":"11","created_by":1,"target_audience":"donors"}
[2025-07-21 12:05:19] INFO: User logged out Context: {"user_id":1,"username":"admin"}
[2025-07-21 12:05:51] SECURITY: LOGIN_FAILED_USER_NOT_FOUND Context: {"event":"LOGIN_FAILED_USER_NOT_FOUND","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"<EMAIL>"}}
[2025-07-21 12:06:16] SECURITY: LOGIN_FAILED_USER_NOT_FOUND Context: {"event":"LOGIN_FAILED_USER_NOT_FOUND","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"kevenzyrel"}}
[2025-07-21 12:07:21] SECURITY: LOGIN_SUCCESS Context: {"event":"LOGIN_SUCCESS","ip_address":"127.0.0.1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/SaLin\/login.php","user_id":null,"details":{"username":"user3","user_id":8}}
[2025-07-21 12:07:21] INFO: User logged in Context: {"user_id":8,"username":"user3","user_type":"unified","ip_address":"127.0.0.1"}
[2025-07-21 12:07:45] ERROR: Failed to get profile data Context: {"user_id":8,"error":"Database operation failed"}
[2025-07-21 12:08:26] INFO: User logged out Context: {"user_id":8,"username":"user3"}
