<?php
/**
 * My Blood Requests - Unified Dashboard
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/UnifiedUser.php';
require_once '../classes/BloodRequest.php';

// Start session and check authentication
startSecureSession();
requireUnifiedAccess('../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();
$unifiedUser = new UnifiedUser($currentUser['id']);

// Get current active role from session
$currentRole = $_SESSION['current_role'] ?? $unifiedUser->getPrimaryRole();

// Check if user has recipient role and if recipient is the current active role
if (!$unifiedUser->hasRole('recipient')) {
    redirectWithMessage('index.php', 'You need recipient role to access this page.', 'error');
}

if ($currentRole !== 'recipient') {
    redirectWithMessage('index.php', 'Only recipients can request blood. Please switch to recipient mode to access this feature.', 'error');
}

// Get user's blood requests
$requests = $db->fetchAll("
    SELECT br.*, bt.type as blood_type, u.first_name, u.last_name
    FROM blood_requests br
    JOIN blood_types bt ON br.blood_type_id = bt.id
    JOIN users u ON br.recipient_id = u.id
    WHERE br.recipient_id = ?
    ORDER BY br.created_at DESC
", [$currentUser['id']]);

$pageTitle = 'My Blood Requests';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(90deg, #8B0000 0%, #DC143C 100%);">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-hand-holding-medical"></i> My Blood Requests</h2>
                    <a href="create-request.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> New Request
                    </a>
                </div>

                <?php if (empty($requests)): ?>
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> No Blood Requests</h5>
                        <p>You haven't made any blood requests yet.</p>
                        <a href="create-request.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Your First Request
                        </a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($requests as $request): ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">Request #<?php echo $request['id']; ?></h6>
                                        <span class="badge bg-<?php 
                                            echo $request['status'] === 'pending' ? 'warning' : 
                                                ($request['status'] === 'fulfilled' ? 'success' : 
                                                ($request['status'] === 'cancelled' ? 'secondary' : 'danger')); 
                                        ?>">
                                            <?php echo ucfirst($request['status']); ?>
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Blood Type:</strong> <?php echo htmlspecialchars($request['blood_type']); ?></p>
                                        <p><strong>Units Needed:</strong> <?php echo $request['units_needed']; ?></p>
                                        <p><strong>Urgency:</strong> 
                                            <span class="badge bg-<?php 
                                                echo $request['urgency_level'] === 'critical' ? 'danger' : 
                                                    ($request['urgency_level'] === 'high' ? 'warning' : 'info'); 
                                            ?>">
                                                <?php echo ucfirst($request['urgency_level']); ?>
                                            </span>
                                        </p>
                                        <p><strong>Hospital:</strong> <?php echo htmlspecialchars($request['hospital_name']); ?></p>
                                        <p><strong>Required By:</strong> <?php echo formatDate($request['required_by_date']); ?></p>
                                        <p><strong>Created:</strong> <?php echo formatDate($request['created_at']); ?></p>
                                    </div>
                                    <div class="card-footer">
                                        <small class="text-muted">
                                            Contact: <?php echo htmlspecialchars($request['hospital_contact']); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
