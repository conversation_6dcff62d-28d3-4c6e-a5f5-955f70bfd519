<?php
/**
 * Unified Dashboard
 * Blood Donation Management System
 * 
 * Single dashboard that adapts based on user roles
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/UnifiedUser.php';
require_once '../classes/Donor.php';
require_once '../classes/Recipient.php';
require_once '../classes/BloodRequest.php';
require_once '../classes/Notification.php';

// Start session and check authentication
startSecureSession();
requireUnifiedAccess('../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();

// Load unified user
$unifiedUser = new UnifiedUser($currentUser['id']);

// Get user roles
$userRoles = $unifiedUser->getActiveRoles();
$primaryRole = $unifiedUser->getPrimaryRole();

// Handle users with no roles - show welcome screen instead of redirecting
$hasNoRoles = empty($userRoles);

// Get current active role from session or use primary role
$currentRole = null;
if (!$hasNoRoles) {
    $currentRole = $_SESSION['current_role'] ?? $primaryRole;

    // Validate current role
    if (!in_array($currentRole, $userRoles)) {
        $currentRole = $userRoles[0]; // Default to first available role
        $_SESSION['current_role'] = $currentRole;
    }
}

// Handle role switching
if (!$hasNoRoles && isset($_POST['switch_role']) && in_array($_POST['switch_role'], $userRoles)) {
    $currentRole = $_POST['switch_role'];
    $_SESSION['current_role'] = $currentRole;
    header('Location: index.php');
    exit;
}

// Load role-specific data
$donorData = null;
$recipientData = null;
$donorStats = null;
$recipientStats = null;

if (!$hasNoRoles) {
    if (in_array('donor', $userRoles)) {
        $donor = $unifiedUser->getDonorInstance();
        if ($donor) {
            $donorData = $donor;
            $donorStats = $donor->getDonationStatistics();
        }
    }

    if (in_array('recipient', $userRoles)) {
        $recipient = $unifiedUser->getRecipientInstance();
        if ($recipient) {
            $recipientData = $recipient;
            $recipientStats = $recipient->getRequestStatistics();
        }
    }
}

// Get notifications
$notificationResult = Notification::getUserNotifications($currentUser['id'], 1, 5);
$notifications = $notificationResult['notifications'];

// Get system announcements
$announcements = [];
if (!$hasNoRoles) {
    $announcements = $db->fetchAll("
        SELECT sa.*, u.first_name, u.last_name
        FROM system_announcements sa
        JOIN users u ON sa.created_by = u.id
        WHERE sa.is_active = TRUE
        AND (sa.expires_at IS NULL OR sa.expires_at > NOW())
        AND (JSON_CONTAINS(sa.target_roles, '\"all\"') OR JSON_CONTAINS(sa.target_roles, ?) OR JSON_CONTAINS(sa.target_roles, ?))
        ORDER BY sa.is_pinned DESC, sa.created_at DESC
        LIMIT 3
    ", ['"' . $currentRole . '"', '"unified"']);
} else {
    // For users with no roles, show general announcements
    $announcements = $db->fetchAll("
        SELECT sa.*, u.first_name, u.last_name
        FROM system_announcements sa
        JOIN users u ON sa.created_by = u.id
        WHERE sa.is_active = TRUE
        AND (sa.expires_at IS NULL OR sa.expires_at > NOW())
        AND (JSON_CONTAINS(sa.target_roles, '\"all\"') OR JSON_CONTAINS(sa.target_roles, '\"unified\"'))
        ORDER BY sa.is_pinned DESC, sa.created_at DESC
        LIMIT 3
    ");
}

// Get flash message
$flash = getFlashMessage();

// Handle success message from role addition
if (isset($_GET['success']) && $_GET['success'] === 'role_added' && isset($_GET['role'])) {
    $addedRole = htmlspecialchars($_GET['role']);
    $flash = [
        'type' => 'success',
        'message' => 'Role added successfully! You can now access ' . ucfirst($addedRole) . ' features.'
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .role-selection-card {
            transition: all 0.3s ease;
        }

        .role-selection-card:hover {
            transform: translateY(-5px);
        }

        @media (max-width: 768px) {
            .role-selection-card {
                margin-bottom: 1rem;
            }

            .card-body h4 {
                font-size: 1.25rem;
            }

            .fa-4x {
                font-size: 3rem !important;
            }
        }

        .blood-theme {
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%);
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(90deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 2px 10px rgba(139,0,0,0.3);">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-dashboard"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user"></i> Profile
                        </a>
                    </li>
                    <?php if ($currentRole === 'donor'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart"></i> My Donations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.php">
                            <i class="fas fa-calendar"></i> My Scheduled Donations
                        </a>
                    </li>
                    <?php elseif ($currentRole === 'recipient'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical"></i> My Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="create-request.php">
                            <i class="fas fa-plus"></i> Request Blood
                        </a>
                    </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a class="nav-link" href="../chat/">
                            <i class="fas fa-comments"></i> Messages
                            <?php
                            $unreadCount = getUnreadMessageCount($currentUser['id']);
                            if ($unreadCount > 0):
                            ?>
                                <span class="badge bg-danger ms-1"><?php echo $unreadCount; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Role Switcher -->
                    <?php if (count($userRoles) > 1): ?>
                    <li class="nav-item">
                        <form method="POST" class="d-inline">
                            <select name="switch_role" class="form-select form-select-sm" style="width: auto; display: inline-block;" onchange="this.form.submit()">
                                <option value="">
                                    <i class="fas fa-user-tag"></i> <?php echo ucfirst($currentRole); ?> Mode
                                </option>
                                <?php foreach ($userRoles as $role): ?>
                                    <?php if ($role !== $currentRole): ?>
                                    <option value="<?php echo $role; ?>">
                                        Switch to <?php echo ucfirst($role); ?>
                                    </option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </form>
                    </li>
                    <?php endif; ?>
                    
                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($currentUser['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card blood-theme">
                    <div class="card-body text-white" style="position: relative; overflow: hidden;">
                        <!-- Blood drop decorative elements -->
                        <div style="position: absolute; top: -10px; right: 20px; font-size: 60px; opacity: 0.1; color: #fff;">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div style="position: absolute; bottom: -15px; left: 30px; font-size: 40px; opacity: 0.1; color: #fff;">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3); font-weight: 700;">
                                    <i class="fas fa-heart" style="color: #FFB6C1; animation: heartbeat 1.5s ease-in-out infinite;"></i> 
                                    Welcome back, <?php echo htmlspecialchars($currentUser['first_name']); ?>!
                                </h2>
                                <p class="mb-2" style="font-size: 1.1em; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                                    <?php if ($hasNoRoles): ?>
                                        Welcome to our blood donation community! Please select your role below to get started.
                                    <?php elseif ($currentRole === 'donor'): ?>
                                        Thank you for being a blood donor. Your contributions save lives!
                                    <?php elseif ($currentRole === 'recipient'): ?>
                                        We're here to help you with your blood needs. Stay strong!
                                    <?php else: ?>
                                        Welcome to your unified dashboard. You can switch between roles anytime.
                                    <?php endif; ?>
                                </p>
                                <?php if (!$hasNoRoles): ?>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php foreach ($userRoles as $role): ?>
                                        <span class="badge <?php echo $role === $currentRole ? 'bg-light text-dark' : 'bg-secondary'; ?>"
                                              style="font-size: 0.9em; padding: 6px 12px;">
                                            <i class="fas fa-<?php echo $role === 'donor' ? 'heart' : 'hand-holding-medical'; ?>"></i>
                                            <?php echo ucfirst($role); ?>
                                            <?php echo $role === $currentRole ? ' (Active)' : ''; ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4 text-end">
                                <?php if ($hasNoRoles): ?>
                                    <div class="alert mb-0" style="background: rgba(255,255,255,0.2); border: 2px solid #FFB6C1; color: white;">
                                        <i class="fas fa-star" style="color: #FFB6C1;"></i>
                                        Get Started!<br>
                                        <small>Choose your role below to access all features.</small>
                                    </div>
                                <?php elseif (count($userRoles) > 1): ?>
                                    <div class="alert mb-0" style="background: rgba(255,255,255,0.2); border: 2px solid #FFB6C1; color: white;">
                                        <i class="fas fa-info-circle" style="color: #FFB6C1;"></i>
                                        You have multiple roles!<br>
                                        <small>Use the role switcher above to change modes.</small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Selection/Management Section -->
        <?php if ($hasNoRoles): ?>
        <!-- New User Role Selection -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                    <div class="card-header bg-transparent border-0 text-center py-4">
                        <h3 class="mb-2" style="color: #8B0000; font-weight: 700;">
                            <i class="fas fa-star text-warning"></i> Choose Your Role
                        </h3>
                        <p class="text-muted mb-0">Select how you'd like to participate in our blood donation community</p>
                    </div>
                    <div class="card-body px-5 pb-5">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="card h-100 border-0 shadow-sm role-selection-card" style="transition: all 0.3s ease; cursor: pointer;"
                                     onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 25px rgba(220,20,60,0.3)';"
                                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)';">
                                    <div class="card-body text-center p-4">
                                        <div class="mb-4">
                                            <i class="fas fa-heart fa-4x" style="color: #DC143C;"></i>
                                        </div>
                                        <h4 class="card-title mb-3" style="color: #8B0000; font-weight: 600;">Blood Donor</h4>
                                        <p class="card-text text-muted mb-4">
                                            Join thousands of heroes who save lives by donating blood.
                                            Your donation can help up to 3 people in need.
                                        </p>
                                        <ul class="list-unstyled text-start mb-4">
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> View scheduled donations</li>
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Track donation history</li>
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Receive donation reminders</li>
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Connect with recipients</li>
                                        </ul>
                                        <a href="add-role.php?role=donor" class="btn btn-danger btn-lg px-4 py-2" style="border-radius: 25px; font-weight: 600;">
                                            <i class="fas fa-heart me-2"></i>Become a Donor
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100 border-0 shadow-sm role-selection-card" style="transition: all 0.3s ease; cursor: pointer;"
                                     onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 8px 25px rgba(0,123,255,0.3)';"
                                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.1)';">
                                    <div class="card-body text-center p-4">
                                        <div class="mb-4">
                                            <i class="fas fa-hand-holding-medical fa-4x" style="color: #007bff;"></i>
                                        </div>
                                        <h4 class="card-title mb-3" style="color: #0056b3; font-weight: 600;">Blood Recipient</h4>
                                        <p class="card-text text-muted mb-4">
                                            Get the support you need when facing medical challenges.
                                            Connect with our community for blood assistance.
                                        </p>
                                        <ul class="list-unstyled text-start mb-4">
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Request blood when needed</li>
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Track request status</li>
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Emergency request support</li>
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i> Connect with donors</li>
                                        </ul>
                                        <a href="add-role.php?role=recipient" class="btn btn-primary btn-lg px-4 py-2" style="border-radius: 25px; font-weight: 600;">
                                            <i class="fas fa-hand-holding-medical me-2"></i>Become a Recipient
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-4">
                            <p class="text-muted mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                You can always add additional roles later from your dashboard
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php elseif (count($userRoles) < 2): ?>
        <!-- Existing User Role Addition -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus-circle"></i> Add More Roles</h5>
                    </div>
                    <div class="card-body">
                        <p>You can add more roles to your account to access additional features:</p>
                        <div class="row">
                            <?php if (!in_array('donor', $userRoles)): ?>
                            <div class="col-md-6">
                                <div class="card border-danger">
                                    <div class="card-body text-center">
                                        <i class="fas fa-heart fa-3x text-danger mb-3"></i>
                                        <h6>Become a Donor</h6>
                                        <p class="text-muted">Help save lives by donating blood</p>
                                        <a href="add-role.php?role=donor" class="btn btn-danger">Add Donor Role</a>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php if (!in_array('recipient', $userRoles)): ?>
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-hand-holding-medical fa-3x text-primary mb-3"></i>
                                        <h6>Become a Recipient</h6>
                                        <p class="text-muted">Request blood when you need it</p>
                                        <a href="add-role.php?role=recipient" class="btn btn-primary">Add Recipient Role</a>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- System Announcements -->
        <?php if (!empty($announcements)): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bullhorn"></i> System Announcements</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($announcements as $announcement): ?>
                            <div class="alert alert-<?php echo $announcement['announcement_type'] === 'urgent' ? 'danger' : ($announcement['announcement_type'] === 'warning' ? 'warning' : 'info'); ?> <?php echo $announcement['is_pinned'] ? 'border-3' : ''; ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="alert-heading">
                                            <?php if ($announcement['is_pinned']): ?>
                                                <i class="fas fa-thumbtack"></i>
                                            <?php endif; ?>
                                            <?php echo htmlspecialchars($announcement['title']); ?>
                                        </h6>
                                        <p class="mb-1"><?php echo nl2br(htmlspecialchars($announcement['content'])); ?></p>
                                        <small class="text-muted">
                                            By <?php echo htmlspecialchars($announcement['first_name'] . ' ' . $announcement['last_name']); ?>
                                            on <?php echo formatDate($announcement['created_at']); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Role-specific Dashboard Content -->
        <?php if ($currentRole === 'donor' && $donorData): ?>
            <!-- Donor Dashboard Content -->
            <?php if ($currentUser['user_type'] === 'admin'): ?>
            <!-- Admin-Only Statistics Cards for Donors -->
            <div class="row mb-4">
                <!-- Donor Statistics Cards -->
                <div class="col-md-3">
                    <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 4px 15px rgba(139,0,0,0.2);">
                        <div class="card-body position-relative">
                            <h5 class="card-title">Total Donations</h5>
                            <p class="card-text"><?php echo number_format($donorStats['completed_donations'] ?? 0); ?></p>
                            <i class="fas fa-heart card-icon" style="animation: heartbeat 2s ease-in-out infinite;"></i>
                        </div>
                        <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                            <small>Completed donations</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #B22222 0%, #DC143C 100%); box-shadow: 0 4px 15px rgba(178,34,34,0.2);">
                        <div class="card-body position-relative">
                            <h5 class="card-title">Units Donated</h5>
                            <p class="card-text"><?php echo number_format($donorStats['total_units_donated'] ?? 0); ?></p>
                            <i class="fas fa-tint card-icon" style="animation: bloodDrop 3s ease-in-out infinite;"></i>
                        </div>
                        <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                            <small>Total units</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #800000 0%, #B22222 100%); box-shadow: 0 4px 15px rgba(128,0,0,0.2);">
                        <div class="card-body position-relative">
                            <h5 class="card-title">Scheduled</h5>
                            <p class="card-text"><?php echo number_format($donorStats['scheduled_donations'] ?? 0); ?></p>
                            <i class="fas fa-calendar card-icon"></i>
                        </div>
                        <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                            <small>Upcoming donations</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #A0522D 0%, #8B0000 100%); box-shadow: 0 4px 15px rgba(160,82,45,0.2);">
                        <div class="card-body position-relative">
                            <h5 class="card-title">Eligibility</h5>
                            <p class="card-text">
                                <?php
                                $isEligible = $donorData->isEligibleForDonation();
                                echo $isEligible ? 'Eligible' : 'Not Eligible';
                                ?>
                            </p>
                            <i class="fas fa-<?php echo $isEligible ? 'check-circle' : 'times-circle'; ?> card-icon"></i>
                        </div>
                        <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                            <small>Current status</small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Donor Information Only (Quick Actions removed for non-admin donors) -->
            <div class="row mb-4">
                <div class="col-md-6 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle"></i> Donor Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-sm-6"><strong>Blood Type:</strong></div>
                                <div class="col-sm-6">
                                    <span class="badge bg-danger"><?php echo htmlspecialchars($donorData->getBloodType()); ?></span>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-6"><strong>Last Donation:</strong></div>
                                <div class="col-sm-6">
                                    <?php echo $donorData->getLastDonationDate() ? formatDate($donorData->getLastDonationDate()) : 'Never'; ?>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-6"><strong>Total Donations:</strong></div>
                                <div class="col-sm-6"><?php echo number_format($donorData->getTotalDonations()); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php elseif ($currentRole === 'recipient' && $recipientData): ?>
            <!-- Recipient Dashboard Content -->
            <?php if ($currentUser['user_type'] === 'admin'): ?>
            <!-- Admin-Only Statistics Cards for Recipients -->
            <div class="row mb-4">
                <!-- Recipient Statistics Cards -->
                <div class="col-md-3">
                    <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 4px 15px rgba(139,0,0,0.2);">
                        <div class="card-body position-relative">
                            <h5 class="card-title">Total Requests</h5>
                            <p class="card-text"><?php echo number_format($recipientStats['total_requests'] ?? 0); ?></p>
                            <i class="fas fa-hand-holding-medical card-icon"></i>
                        </div>
                        <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                            <small>All time requests</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #B22222 0%, #DC143C 100%); box-shadow: 0 4px 15px rgba(178,34,34,0.2);">
                        <div class="card-body position-relative">
                            <h5 class="card-title">Active Requests</h5>
                            <p class="card-text"><?php echo number_format($recipientStats['active_requests'] ?? 0); ?></p>
                            <i class="fas fa-clock card-icon"></i>
                        </div>
                        <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                            <small>Currently active</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #800000 0%, #B22222 100%); box-shadow: 0 4px 15px rgba(128,0,0,0.2);">
                        <div class="card-body position-relative">
                            <h5 class="card-title">Fulfilled</h5>
                            <p class="card-text"><?php echo number_format($recipientStats['fulfilled_requests'] ?? 0); ?></p>
                            <i class="fas fa-check-circle card-icon"></i>
                        </div>
                        <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                            <small>Successfully fulfilled</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card dashboard-card text-white" style="background: linear-gradient(135deg, #A0522D 0%, #8B0000 100%); box-shadow: 0 4px 15px rgba(160,82,45,0.2);">
                        <div class="card-body position-relative">
                            <h5 class="card-title">Units Received</h5>
                            <p class="card-text"><?php echo number_format($recipientStats['total_units_received'] ?? 0); ?></p>
                            <i class="fas fa-tint card-icon"></i>
                        </div>
                        <div class="card-footer" style="background: rgba(0,0,0,0.2);">
                            <small>Total units</small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Recipient Quick Actions -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-plus"></i> Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="create-request.php" class="btn btn-danger">
                                    <i class="fas fa-plus"></i> Create Blood Request
                                </a>
                            </div>
                            <div class="mt-3 p-3 bg-light rounded">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-info-circle text-primary me-2 mt-1"></i>
                                    <div>
                                        <small class="text-muted">
                                            <strong>Need Help?</strong> Use the navigation menu to view your requests or contact administrators for assistance.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle"></i> Emergency Contacts</h5>
                        </div>
                        <div class="card-body">
                            <?php if ($recipientData->getEmergencyContact()): ?>
                                <div class="row mb-2">
                                    <div class="col-sm-6"><strong>Emergency Contact:</strong></div>
                                    <div class="col-sm-6"><?php echo htmlspecialchars($recipientData->getEmergencyContact()); ?></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-6"><strong>Emergency Phone:</strong></div>
                                    <div class="col-sm-6"><?php echo htmlspecialchars($recipientData->getEmergencyPhone()); ?></div>
                                </div>
                            <?php endif; ?>
                            <?php if ($recipientData->getDoctorName()): ?>
                                <div class="row mb-2">
                                    <div class="col-sm-6"><strong>Doctor:</strong></div>
                                    <div class="col-sm-6"><?php echo htmlspecialchars($recipientData->getDoctorName()); ?></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-6"><strong>Doctor Contact:</strong></div>
                                    <div class="col-sm-6"><?php echo htmlspecialchars($recipientData->getDoctorContact()); ?></div>
                                </div>
                            <?php endif; ?>
                            <?php if (!$recipientData->getEmergencyContact() && !$recipientData->getDoctorName()): ?>
                                <p class="text-muted">No emergency contacts set up.</p>
                                <a href="profile.php" class="btn btn-sm btn-outline-primary">Update Profile</a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Messaging Section (Admin Only) -->
        <?php if ($currentUser['user_type'] === 'admin'): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-comments"></i> Messaging Center</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="me-3">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <i class="fas fa-envelope fa-lg"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Unread Messages</h6>
                                        <h4 class="mb-0 text-primary"><?php echo number_format(getUnreadMessageCount($currentUser['id'])); ?></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <a href="../chat/" class="btn btn-primary">
                                        <i class="fas fa-comments"></i> Open Messages
                                    </a>
                                    <a href="../chat/find-users-restricted.php" class="btn btn-outline-primary">
                                        <i class="fas fa-user-plus"></i> Find Users to Message
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Recent Notifications -->
        <?php if (!empty($notifications)): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-bell"></i> Recent Notifications</h5>
                            <a href="notifications.php" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php foreach ($notifications as $notification): ?>
                            <div class="d-flex justify-content-between align-items-start mb-3 p-3 border rounded <?php echo !$notification['is_read'] ? 'bg-light' : ''; ?>">
                                <div>
                                    <h6 class="mb-1"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                    <p class="mb-1 text-muted"><?php echo htmlspecialchars($notification['message']); ?></p>
                                    <small class="text-muted"><?php echo formatDate($notification['created_at']); ?></small>
                                </div>
                                <?php if (!$notification['is_read']): ?>
                                    <span class="badge bg-danger">New</span>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Enhanced role switching with smooth transitions
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Bootstrap dropdowns manually
            const dropdownElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
            const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });

            // Store current role in session storage for persistence
            const currentRole = '<?php echo $currentRole ?? ""; ?>';
            if (currentRole) {
                sessionStorage.setItem('currentRole', currentRole);
            }

            // Add smooth transitions to role cards
            const roleCards = document.querySelectorAll('.role-selection-card');
            roleCards.forEach(card => {
                card.addEventListener('click', function() {
                    const link = this.querySelector('a');
                    if (link) {
                        // Add loading state
                        const btn = link;
                        const originalText = btn.innerHTML;
                        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                        btn.classList.add('disabled');

                        // Navigate after short delay for visual feedback
                        setTimeout(() => {
                            window.location.href = link.href;
                        }, 500);
                    }
                });
            });

            // Enhanced role switcher with confirmation
            const roleSwitchForms = document.querySelectorAll('form[method="POST"]');
            roleSwitchForms.forEach(form => {
                const switchButton = form.querySelector('button[name="switch_role"]');
                if (switchButton) {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();

                        const newRole = switchButton.value;
                        const currentRoleText = '<?php echo ucfirst($currentRole ?? ""); ?>';
                        const newRoleText = newRole.charAt(0).toUpperCase() + newRole.slice(1);

                        // Show confirmation with smooth transition
                        if (confirm(`Switch from ${currentRoleText} to ${newRoleText} mode?`)) {
                            // Add loading state
                            switchButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Switching...';
                            switchButton.disabled = true;

                            // Store preference
                            sessionStorage.setItem('preferredRole', newRole);

                            // Create a new form element to ensure proper submission
                            const newForm = document.createElement('form');
                            newForm.method = 'POST';
                            newForm.action = '';

                            const hiddenInput = document.createElement('input');
                            hiddenInput.type = 'hidden';
                            hiddenInput.name = 'switch_role';
                            hiddenInput.value = newRole;

                            newForm.appendChild(hiddenInput);
                            document.body.appendChild(newForm);
                            newForm.submit();
                        }
                    });
                }
            });

            // Auto-dismiss alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.style.transition = 'opacity 0.5s ease-out';
                        alert.style.opacity = '0';
                        setTimeout(() => {
                            if (alert.parentNode) {
                                alert.remove();
                            }
                        }, 500);
                    }
                }, 5000);
            });

            // Add hover effects to dashboard cards
            const dashboardCards = document.querySelectorAll('.dashboard-card');
            dashboardCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.2s ease';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
