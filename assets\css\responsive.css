/**
 * Mobile-First Responsive Design
 * Blood Donation Management System
 * 
 * This file implements comprehensive responsive design following mobile-first principles
 * Base styles for mobile (320px+), then scale up for larger screens
 */

/* ==========================================================================
   MOBILE-FIRST RESPONSIVE DESIGN
   ========================================================================== */

/* Base Mobile Styles (320px - 767px) */
/* All styles here are the base for mobile devices */

/* Typography Responsive Scale */
html {
    font-size: 14px; /* Base font size for mobile */
}

@media (min-width: 768px) {
    html {
        font-size: 16px; /* Larger base font for tablets and up */
    }
}

@media (min-width: 1024px) {
    html {
        font-size: 18px; /* Largest base font for desktop */
    }
}

/* Responsive Typography */
h1 { font-size: 1.75rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.875rem; }

@media (min-width: 768px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
    h5 { font-size: 1.125rem; }
    h6 { font-size: 1rem; }
}

@media (min-width: 1024px) {
    h1 { font-size: 2.25rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.75rem; }
    h4 { font-size: 1.5rem; }
    h5 { font-size: 1.25rem; }
    h6 { font-size: 1.125rem; }
}

/* ==========================================================================
   LAYOUT RESPONSIVE GRID
   ========================================================================== */

/* Mobile-first container */
.container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
}

@media (min-width: 768px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

@media (min-width: 1024px) {
    .container-fluid {
        padding-left: 3rem;
        padding-right: 3rem;
    }
}

/* Responsive Grid System */
.row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
}

.row > * {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

@media (min-width: 768px) {
    .row {
        margin-left: -1rem;
        margin-right: -1rem;
    }
    
    .row > * {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* ==========================================================================
   NAVIGATION RESPONSIVE
   ========================================================================== */

/* Mobile Navigation */
.navbar {
    padding: 0.75rem 1rem;
}

.navbar-brand {
    font-size: 1.25rem;
    font-weight: 600;
}

.navbar-toggler {
    padding: 0.25rem 0.5rem;
    font-size: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.375rem;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.navbar-nav {
    margin-top: 1rem;
}

.navbar-nav .nav-link {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
    font-size: 1rem;
    font-weight: 500;
}

/* Tablet Navigation */
@media (min-width: 768px) {
    .navbar {
        padding: 1rem 2rem;
    }
    
    .navbar-brand {
        font-size: 1.5rem;
    }
    
    .navbar-nav {
        margin-top: 0;
    }
    
    .navbar-nav .nav-link {
        padding: 0.5rem 1rem;
        margin-bottom: 0;
        font-size: 0.95rem;
    }
}

/* Desktop Navigation */
@media (min-width: 1024px) {
    .navbar {
        padding: 1rem 3rem;
    }
    
    .navbar-brand {
        font-size: 1.75rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1.25rem;
        font-size: 1rem;
    }
}

/* ==========================================================================
   CARDS RESPONSIVE
   ========================================================================== */

/* Mobile Cards */
.card {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    background-color: rgba(0, 0, 0, 0.03);
}

.card-body {
    padding: 1rem;
}

.card-footer {
    padding: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    background-color: rgba(0, 0, 0, 0.03);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.card-text {
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Tablet Cards */
@media (min-width: 768px) {
    .card {
        margin-bottom: 1.5rem;
    }
    
    .card-header {
        padding: 1.25rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
    
    .card-footer {
        padding: 1.25rem;
    }
    
    .card-title {
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }
    
    .card-text {
        font-size: 1rem;
    }
}

/* Desktop Cards */
@media (min-width: 1024px) {
    .card {
        margin-bottom: 2rem;
    }
    
    .card-header {
        padding: 1.5rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .card-footer {
        padding: 1.5rem;
    }
    
    .card-title {
        font-size: 1.375rem;
        margin-bottom: 1.25rem;
    }
    
    .card-text {
        font-size: 1.05rem;
    }
}

/* ==========================================================================
   FORMS RESPONSIVE
   ========================================================================== */

/* Mobile Forms */
.form-control {
    font-size: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    line-height: 1.5;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(139, 0, 0, 0.25);
}

.form-label {
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

.form-text {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-select {
    font-size: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    line-height: 1.5;
}

.form-check {
    margin-bottom: 0.75rem;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
}

.form-check-label {
    font-size: 0.95rem;
    padding-left: 0.5rem;
}

/* Tablet Forms */
@media (min-width: 768px) {
    .form-control {
        font-size: 1rem;
        padding: 0.875rem 1.125rem;
    }
    
    .form-label {
        font-size: 1rem;
        margin-bottom: 0.75rem;
    }
    
    .form-select {
        font-size: 1rem;
        padding: 0.875rem 1.125rem;
    }
    
    .form-check-label {
        font-size: 1rem;
        padding-left: 0.75rem;
    }
}

/* Desktop Forms */
@media (min-width: 1024px) {
    .form-control {
        font-size: 1.05rem;
        padding: 1rem 1.25rem;
    }
    
    .form-label {
        font-size: 1.05rem;
        margin-bottom: 1rem;
    }
    
    .form-select {
        font-size: 1.05rem;
        padding: 1rem 1.25rem;
    }
    
    .form-check-label {
        font-size: 1.05rem;
        padding-left: 1rem;
    }
}

/* ==========================================================================
   BUTTONS RESPONSIVE
   ========================================================================== */

/* Mobile Buttons */
.btn {
    font-size: 0.95rem;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    transition: all 0.15s ease-in-out;
}

.btn-sm {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

.btn-lg {
    font-size: 1.125rem;
    padding: 1rem 2rem;
}

.btn-block {
    display: block;
    width: 100%;
}

/* Tablet Buttons */
@media (min-width: 768px) {
    .btn {
        font-size: 1rem;
        padding: 0.875rem 1.75rem;
    }
    
    .btn-sm {
        font-size: 0.95rem;
        padding: 0.625rem 1.25rem;
    }
    
    .btn-lg {
        font-size: 1.25rem;
        padding: 1.125rem 2.25rem;
    }
}

/* Desktop Buttons */
@media (min-width: 1024px) {
    .btn {
        font-size: 1.05rem;
        padding: 1rem 2rem;
    }
    
    .btn-sm {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }
    
    .btn-lg {
        font-size: 1.375rem;
        padding: 1.25rem 2.5rem;
    }
}

/* ==========================================================================
   TABLES RESPONSIVE
   ========================================================================== */

/* Mobile Tables */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 1rem;
}

.table {
    font-size: 0.875rem;
    margin-bottom: 0;
}

.table th,
.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.table th {
    font-weight: 600;
    font-size: 0.875rem;
    background-color: rgba(0, 0, 0, 0.05);
}

/* Tablet Tables */
@media (min-width: 768px) {
    .table {
        font-size: 0.95rem;
    }
    
    .table th,
    .table td {
        padding: 0.875rem 0.75rem;
    }
    
    .table th {
        font-size: 0.95rem;
    }
}

/* Desktop Tables */
@media (min-width: 1024px) {
    .table {
        font-size: 1rem;
    }
    
    .table th,
    .table td {
        padding: 1rem;
    }
    
    .table th {
        font-size: 1rem;
    }
}

/* ==========================================================================
   DASHBOARD RESPONSIVE
   ========================================================================== */

/* Mobile Dashboard */
.dashboard-card {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dashboard-card .card-body {
    padding: 1rem;
}

.dashboard-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.dashboard-card .card-text {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.dashboard-card .card-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Tablet Dashboard */
@media (min-width: 768px) {
    .dashboard-card {
        margin-bottom: 1.5rem;
    }
    
    .dashboard-card .card-body {
        padding: 1.25rem;
    }
    
    .dashboard-card .card-title {
        font-size: 1.125rem;
        margin-bottom: 1rem;
    }
    
    .dashboard-card .card-text {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }
    
    .dashboard-card .card-icon {
        font-size: 2.25rem;
        margin-bottom: 0.75rem;
    }
}

/* Desktop Dashboard */
@media (min-width: 1024px) {
    .dashboard-card {
        margin-bottom: 2rem;
    }
    
    .dashboard-card .card-body {
        padding: 1.5rem;
    }
    
    .dashboard-card .card-title {
        font-size: 1.25rem;
        margin-bottom: 1.25rem;
    }
    
    .dashboard-card .card-text {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .dashboard-card .card-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
}

/* ==========================================================================
   PROFILE PAGE RESPONSIVE
   ========================================================================== */

/* Mobile Profile */
.profile-photo {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 50%;
}

.profile-photo-preview {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 50%;
}

.nav-tabs {
    border-bottom: 2px solid #dc3545;
    margin-bottom: 1rem;
}

.nav-tabs .nav-link {
    border-radius: 0.5rem 0.5rem 0 0;
    margin-right: 0.25rem;
    color: #6c757d;
    border: 2px solid #dee2e6;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    font-weight: 500;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
}

.tab-content {
    background: #fff;
    border-radius: 0 0 0.75rem 0.75rem;
    padding: 1rem;
    border: 2px solid #dc3545;
}

/* Tablet Profile */
@media (min-width: 768px) {
    .profile-photo {
        width: 150px;
        height: 150px;
        border: 4px solid #fff;
    }
    
    .profile-photo-preview {
        width: 100px;
        height: 100px;
    }
    
    .nav-tabs .nav-link {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        margin-right: 0.5rem;
    }
    
    .tab-content {
        padding: 1.5rem;
    }
}

/* Desktop Profile */
@media (min-width: 1024px) {
    .profile-photo {
        width: 180px;
        height: 180px;
        border: 5px solid #fff;
    }
    
    .profile-photo-preview {
        width: 120px;
        height: 120px;
    }
    
    .nav-tabs .nav-link {
        padding: 1.25rem 2rem;
        font-size: 1.125rem;
        margin-right: 0.75rem;
    }
    
    .tab-content {
        padding: 2rem;
    }
}

/* ==========================================================================
   UTILITY CLASSES RESPONSIVE
   ========================================================================== */

/* Responsive Spacing */
.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.m-4 { margin: 1.5rem !important; }
.m-5 { margin: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

/* Responsive Display */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

@media (min-width: 768px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
    .d-md-inline { display: inline !important; }
    .d-md-inline-block { display: inline-block !important; }
}

@media (min-width: 1024px) {
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
    .d-lg-inline { display: inline !important; }
    .d-lg-inline-block { display: inline-block !important; }
}

/* Responsive Text Alignment */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

@media (min-width: 768px) {
    .text-md-left { text-align: left !important; }
    .text-md-center { text-align: center !important; }
    .text-md-right { text-align: right !important; }
}

@media (min-width: 1024px) {
    .text-lg-left { text-align: left !important; }
    .text-lg-center { text-align: center !important; }
    .text-lg-right { text-align: right !important; }
}

/* ==========================================================================
   TOUCH OPTIMIZATIONS
   ========================================================================== */

/* Touch-friendly elements */
.btn, .nav-link, .form-control, .form-select {
    min-height: 44px; /* Apple's recommended minimum touch target size */
}

.form-check-input {
    min-width: 44px;
    min-height: 44px;
}

/* Touch-friendly spacing */
.navbar-nav .nav-link {
    margin-bottom: 0.5rem;
}

@media (min-width: 768px) {
    .navbar-nav .nav-link {
        margin-bottom: 0;
    }
}

/* ==========================================================================
   PRINT STYLES
   ========================================================================== */

@media print {
    .navbar,
    .btn,
    .nav-tabs,
    .card-footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .container-fluid {
        max-width: 100% !important;
        padding: 0 !important;
    }
}

/* ==========================================================================
   ACCESSIBILITY IMPROVEMENTS
   ========================================================================== */

/* Focus indicators */
.btn:focus,
.form-control:focus,
.form-select:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
} 