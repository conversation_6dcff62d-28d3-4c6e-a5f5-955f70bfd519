<?php
/**
 * Debug Role Switching
 * Blood Donation Management System
 */

require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'classes/UnifiedUser.php';

// Start session and check authentication
startSecureSession();
requireLogin('login.php');

$currentUser = getCurrentUser();
$unifiedUser = new UnifiedUser($currentUser['id']);

echo "<h2>Role Switching Debug Information</h2>";
echo "<p><strong>User ID:</strong> " . $currentUser['id'] . "</p>";
echo "<p><strong>Username:</strong> " . $currentUser['username'] . "</p>";
echo "<p><strong>User Type:</strong> " . $currentUser['user_type'] . "</p>";

// Get user roles
$userRoles = $unifiedUser->getActiveRoles();
echo "<p><strong>Active Roles:</strong> " . implode(', ', $userRoles) . "</p>";

// Get primary role
$primaryRole = $unifiedUser->getPrimaryRole();
echo "<p><strong>Primary Role:</strong> " . ($primaryRole ?: 'None') . "</p>";

// Get current role from session
$currentRole = $_SESSION['current_role'] ?? null;
echo "<p><strong>Session Current Role:</strong> " . ($currentRole ?: 'Not set') . "</p>";

// Determine effective current role
$effectiveCurrentRole = null;
if (!empty($userRoles)) {
    $effectiveCurrentRole = $currentRole ?? $primaryRole;
    
    // Validate current role
    if (!in_array($effectiveCurrentRole, $userRoles)) {
        $effectiveCurrentRole = $userRoles[0]; // Default to first available role
    }
}
echo "<p><strong>Effective Current Role:</strong> " . ($effectiveCurrentRole ?: 'None') . "</p>";

// Show session data
echo "<h3>Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Handle role switching
if (isset($_POST['switch_role']) && in_array($_POST['switch_role'], $userRoles)) {
    $newRole = $_POST['switch_role'];
    $_SESSION['current_role'] = $newRole;
    echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<strong>Role switched to:</strong> " . $newRole;
    echo "</div>";
    echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
}

// Show role switching form
if (count($userRoles) > 1) {
    echo "<h3>Role Switching Form:</h3>";
    echo "<form method='POST' style='margin: 20px 0;'>";
    foreach ($userRoles as $role) {
        if ($role !== $effectiveCurrentRole) {
            echo "<button type='submit' name='switch_role' value='" . $role . "' style='margin: 5px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>";
            echo "Switch to " . ucfirst($role);
            echo "</button>";
        }
    }
    echo "</form>";
}

// Test role-specific content
echo "<h3>Role-Specific Content Test:</h3>";
if ($effectiveCurrentRole === 'donor') {
    echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<strong>DONOR MODE:</strong> Thank you for being a blood donor. Your contributions save lives!";
    echo "</div>";
} elseif ($effectiveCurrentRole === 'recipient') {
    echo "<div style='background: #d1ecf1; padding: 10px; border: 1px solid #bee5eb; border-radius: 5px;'>";
    echo "<strong>RECIPIENT MODE:</strong> We're here to help you with your blood needs. Stay strong!";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<strong>NO SPECIFIC ROLE:</strong> Welcome to your unified dashboard.";
    echo "</div>";
}

echo "<br><a href='dashboard/'>Back to Dashboard</a>";
?>
