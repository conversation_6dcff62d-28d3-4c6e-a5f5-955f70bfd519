<?php
/**
 * Test Role Switch Form Submission
 * Blood Donation Management System
 */

require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'classes/UnifiedUser.php';

// Start session and check authentication
startSecureSession();
requireLogin('login.php');

$currentUser = getCurrentUser();
$unifiedUser = new UnifiedUser($currentUser['id']);

echo "<h2>Role Switch Form Test</h2>";

// Handle role switching
if (isset($_POST['switch_role'])) {
    echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<strong>Form submitted!</strong><br>";
    echo "POST data received: switch_role = " . $_POST['switch_role'] . "<br>";
    
    $userRoles = $unifiedUser->getActiveRoles();
    echo "User roles: " . implode(', ', $userRoles) . "<br>";
    
    if (in_array($_POST['switch_role'], $userRoles)) {
        $_SESSION['current_role'] = $_POST['switch_role'];
        echo "Session updated: current_role = " . $_SESSION['current_role'] . "<br>";
        echo "<strong>Role switch successful!</strong>";
    } else {
        echo "<strong>Error: Invalid role</strong>";
    }
    echo "</div>";
}

// Get user roles
$userRoles = $unifiedUser->getActiveRoles();
$currentRole = $_SESSION['current_role'] ?? ($userRoles[0] ?? null);

echo "<p><strong>Current Role:</strong> " . ($currentRole ?: 'None') . "</p>";
echo "<p><strong>Available Roles:</strong> " . implode(', ', $userRoles) . "</p>";

// Show simple form
if (count($userRoles) > 1) {
    echo "<h3>Simple Role Switch Form (No JavaScript):</h3>";
    foreach ($userRoles as $role) {
        if ($role !== $currentRole) {
            echo "<form method='POST' style='display: inline-block; margin: 5px;'>";
            echo "<button type='submit' name='switch_role' value='" . $role . "' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>";
            echo "Switch to " . ucfirst($role);
            echo "</button>";
            echo "</form>";
        }
    }
}

echo "<br><br><a href='dashboard/'>Back to Dashboard</a>";
?>
