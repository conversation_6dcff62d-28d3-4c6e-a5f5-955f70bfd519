<?php
/**
 * Unified Dashboard Schedule Donation (View Only)
 * Blood Donation Management System
 * 
 * Note: Users can only view their scheduled donations.
 * Only administrators can create or modify donation schedules.
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../classes/Donor.php';
require_once '../classes/BloodRequest.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');

$currentUser = getCurrentUser();

// Only allow unified users
if ($currentUser['user_type'] !== 'unified') {
    $redirectUrl = match($currentUser['user_type']) {
        USER_TYPE_ADMIN => '../admin/',
        USER_TYPE_DONOR => '../donor/',
        USER_TYPE_RECIPIENT => '../recipient/',
        default => '../login.php'
    };
    redirectWithMessage($redirectUrl, 'Please use your dedicated dashboard.', 'info');
}

// Check if user has donor role
$userRoles = getUserRoles($currentUser['id']);
if (!in_array('donor', $userRoles)) {
    redirectWithMessage('index.php', 'You need to have a donor role to view donation schedules.', 'error');
}

$db = Database::getInstance();

// Load donor profile
$donor = new Donor($currentUser['id']);

// Get donor's scheduled and upcoming donations
$scheduledDonations = $donor->getUpcomingDonations();

// Get donation statistics
$donorStats = $donor->getDonationStatistics();

$csrfToken = generateCSRFToken();

// Get flash message
$flash = getFlashMessage();

$pageTitle = 'My Scheduled Donations - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?> - Dashboard
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-dashboard"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="donations.php">
                            <i class="fas fa-heart"></i> My Donations
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="requests.php">
                            <i class="fas fa-hand-holding-medical"></i> Blood Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="schedule.php">
                            <i class="fas fa-calendar"></i> My Scheduled Donations
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($currentUser['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if ($flash): ?>
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-calendar"></i> My Scheduled Donations
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Information Alert -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Schedule Management:</strong> Donation schedules are managed by administrators only. 
                            You can view your scheduled donations below. If you need to schedule a new donation or modify an existing one, 
                            please contact an administrator.
                        </div>

                        <!-- Donation Statistics -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5><i class="fas fa-calendar-check"></i> Scheduled</h5>
                                        <h3><?php echo number_format($donorStats['scheduled_donations'] ?? 0); ?></h3>
                                        <p class="mb-0">Upcoming Donations</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5><i class="fas fa-heart"></i> Completed</h5>
                                        <h3><?php echo number_format($donorStats['total_donations'] ?? 0); ?></h3>
                                        <p class="mb-0">Total Donations</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h5><i class="fas fa-tint"></i> Blood Type</h5>
                                        <h3><?php echo htmlspecialchars($donor->getBloodType()); ?></h3>
                                        <p class="mb-0">Your Blood Type</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Scheduled Donations List -->
                        <h5><i class="fas fa-list"></i> My Scheduled Donations</h5>
                        
                        <?php if (empty($scheduledDonations)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>No scheduled donations found.</strong><br>
                                You don't have any upcoming donation appointments scheduled. 
                                To schedule a donation, please contact an administrator.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th><i class="fas fa-calendar"></i> Date</th>
                                            <th><i class="fas fa-clock"></i> Time</th>
                                            <th><i class="fas fa-map-marker-alt"></i> Location</th>
                                            <th><i class="fas fa-tint"></i> Blood Type</th>
                                            <th><i class="fas fa-info-circle"></i> Status</th>
                                            <th><i class="fas fa-tools"></i> Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($scheduledDonations as $donation): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo date('M d, Y', strtotime($donation['donation_date'])); ?></strong>
                                                </td>
                                                <td>
                                                    <?php echo date('h:i A', strtotime($donation['donation_date'])); ?>
                                                </td>
                                                <td>
                                                    <i class="fas fa-map-marker-alt text-danger"></i>
                                                    <?php echo htmlspecialchars($donation['location']); ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">
                                                        <?php echo htmlspecialchars($donation['blood_type']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($donation['status'] === 'scheduled'): ?>
                                                        <span class="badge bg-warning">
                                                            <i class="fas fa-clock"></i> Scheduled
                                                        </span>
                                                    <?php elseif ($donation['status'] === 'completed'): ?>
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check"></i> Completed
                                                        </span>
                                                    <?php elseif ($donation['status'] === 'cancelled'): ?>
                                                        <span class="badge bg-danger">
                                                            <i class="fas fa-times"></i> Cancelled
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <button type="button" class="btn btn-outline-info" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#donationModal<?php echo $donation['id']; ?>">
                                                            <i class="fas fa-eye"></i> View
                                                        </button>
                                                        <?php if ($donation['status'] === 'scheduled'): ?>
                                                            <button type="button" class="btn btn-outline-warning" disabled>
                                                                <i class="fas fa-edit"></i> Edit
                                                            </button>
                                                            <button type="button" class="btn btn-outline-danger" disabled>
                                                                <i class="fas fa-times"></i> Cancel
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                            
                                            <!-- Donation Details Modal -->
                                            <div class="modal fade" id="donationModal<?php echo $donation['id']; ?>" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">
                                                                <i class="fas fa-heart"></i> Donation Details
                                                            </h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <p><strong>Date:</strong> <?php echo date('M d, Y', strtotime($donation['donation_date'])); ?></p>
                                                                    <p><strong>Time:</strong> <?php echo date('h:i A', strtotime($donation['donation_date'])); ?></p>
                                                                    <p><strong>Location:</strong> <?php echo htmlspecialchars($donation['location']); ?></p>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <p><strong>Blood Type:</strong> <?php echo htmlspecialchars($donation['blood_type']); ?></p>
                                                                    <p><strong>Status:</strong> 
                                                                        <?php if ($donation['status'] === 'scheduled'): ?>
                                                                            <span class="badge bg-warning">Scheduled</span>
                                                                        <?php elseif ($donation['status'] === 'completed'): ?>
                                                                            <span class="badge bg-success">Completed</span>
                                                                        <?php elseif ($donation['status'] === 'cancelled'): ?>
                                                                            <span class="badge bg-danger">Cancelled</span>
                                                                        <?php endif; ?>
                                                                    </p>
                                                                    <?php if (!empty($donation['notes'])): ?>
                                                                        <p><strong>Notes:</strong> <?php echo htmlspecialchars($donation['notes']); ?></p>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>

                        <!-- Contact Information -->
                        <div class="alert alert-light mt-4">
                            <h6><i class="fas fa-phone"></i> Need to Schedule a Donation?</h6>
                            <p class="mb-2">To schedule a new donation or modify an existing appointment, please contact an administrator:</p>
                            <ul class="mb-0">
                                <li>Email: <EMAIL></li>
                                <li>Phone: (*************</li>
                                <li>Or visit the main office during business hours</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
