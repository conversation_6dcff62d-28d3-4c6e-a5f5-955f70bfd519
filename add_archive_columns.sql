-- Add archive columns to blood_requests table
ALTER TABLE blood_requests 
ADD COLUMN is_archived BOOLEAN DEFAULT FALSE,
ADD COLUMN archived_at TIMESTAMP NULL,
ADD COLUMN archived_by INT NULL,
ADD INDEX idx_archived (is_archived),
ADD INDEX idx_archived_at (archived_at),
ADD FOREIGN KEY (archived_by) REFERENCES users(id) ON DELETE SET NULL;

-- Add archive columns to users table for user archiving
ALTER TABLE users 
ADD COLUMN is_archived BOOLEAN DEFAULT FALSE,
ADD COLUMN archived_at TIMESTAMP NULL,
ADD COLUMN archived_by INT NULL,
ADD INDEX idx_user_archived (is_archived),
ADD INDEX idx_user_archived_at (archived_at),
ADD FOREIGN KEY (archived_by) REFERENCES users(id) ON DELETE SET NULL;

-- Add archive columns to donations table for donation archiving
ALTER TABLE donations
ADD COLUMN is_archived BOOLEAN DEFAULT FALSE,
ADD COLUMN archived_at TIMESTAMP NULL,
ADD COLUMN archived_by INT NULL,
ADD INDEX idx_donation_archived (is_archived),
ADD INDEX idx_donation_archived_at (archived_at),
ADD FOREIGN KEY (archived_by) REFERENCES users(id) ON DELETE SET NULL;

-- Add archive columns to donation_centers table for center archiving
ALTER TABLE donation_centers
ADD COLUMN is_archived BOOLEAN DEFAULT FALSE,
ADD COLUMN archived_at TIMESTAMP NULL,
ADD COLUMN archived_by INT NULL,
ADD INDEX idx_center_archived (is_archived),
ADD INDEX idx_center_archived_at (archived_at),
ADD FOREIGN KEY (archived_by) REFERENCES users(id) ON DELETE SET NULL;
