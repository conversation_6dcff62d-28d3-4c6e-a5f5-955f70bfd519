# Unified Blood Donation System - User Guide

## Overview

The Blood Donation Management System has been upgraded to provide a unified user experience. Users can now create a single account and choose their roles (Blood Donor, Blood Recipient, or both) after registration.

## Key Changes

### 1. Simplified Registration Process
- **Before**: Users had to select their role during registration and provide role-specific information
- **After**: Users create a basic account with only essential information (name, email, password, contact details)
- **Benefit**: Faster registration process, users can explore the system before committing to a role

### 2. Post-Registration Role Selection
- **New Feature**: After registration, users are redirected to a unified dashboard
- **Role Options**: Users can choose to become a Blood Donor, Blood Recipient, or both
- **Flexibility**: Users can add additional roles at any time from their dashboard

### 3. Unified Dashboard
- **Single Interface**: One dashboard that adapts based on user roles
- **Role Switching**: Users with multiple roles can easily switch between them
- **Welcome Screen**: New users see prominent role selection options

## User Journey

### For New Users

1. **Registration**
   - Visit `register.php`
   - Fill in basic information: username, email, password, name, phone, address
   - No role selection required during registration
   - Click "Create Account"

2. **Dashboard Welcome**
   - After registration, users are redirected to the unified dashboard
   - See welcome message: "Welcome to our blood donation community! Please select your role below to get started."
   - Two prominent options available:
     - **"Become a Blood Donor"** (red theme with heart icon)
     - **"Become a Blood Recipient"** (blue theme with medical icon)

3. **Role Application**
   - Click on desired role button
   - Fill out role-specific application form:
     - **Donor**: Blood type, birth date, weight, medical conditions
     - **Recipient**: Medical condition, emergency contacts, doctor information
   - Submit application via AJAX (no page reload)
   - Receive confirmation and return to dashboard

4. **Dashboard Access**
   - Dashboard now shows role-specific content
   - Access to role-specific features and statistics
   - Option to add additional roles if desired

### For Existing Users

- Existing users retain all their data and functionality
- No changes to their current workflow
- Can still access all existing features

## Technical Implementation

### Database Changes

1. **New Tables**
   - `user_roles`: Manages multiple roles per user
   - `system_announcements`: System-wide announcements
   - `schema_migrations`: Tracks database changes

2. **Updated Tables**
   - `users`: Added unified user fields (`is_unified_user`, `primary_role`, etc.)
   - `donor_profiles` & `recipient_profiles`: Added application status fields

### File Changes

1. **Modified Files**
   - `register.php`: Simplified to basic account creation
   - `dashboard/index.php`: Enhanced with role selection interface
   - `dashboard/add-role.php`: Role application forms with AJAX
   - `admin/users.php`: Updated to show unified user information
   - `includes/validation.php`: Added basic registration validation
   - `classes/UnifiedUser.php`: Added `createBasicAccount` method

2. **New Files**
   - `admin/user-details.php`: Comprehensive user management interface
   - `database/migrate_to_unified_system.sql`: Database migration script
   - `database/run_migration.php`: Migration runner
   - `test_unified_system.php`: Comprehensive testing script

## Admin Panel Enhancements

### User Management
- **Unified View**: See all users with their roles in one table
- **Role Indicators**: Visual badges showing user roles (None, Donor, Recipient, Both)
- **Detailed Profiles**: Click on any user to see comprehensive profile with tabs for:
  - Basic Information
  - Donor Profile (if applicable)
  - Recipient Profile (if applicable)
  - Recent Activity

### User Actions
- **Suspend/Activate**: Manage user account status
- **Delete**: Remove users (with confirmation)
- **Role Management**: View and manage user roles

## Installation & Migration

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Existing blood donation system

### Migration Steps

1. **Backup Database**
   ```bash
   mysqldump -u username -p blood_donation_system > backup_$(date +%Y%m%d).sql
   ```

2. **Run Migration**
   ```bash
   php database/run_migration.php
   ```
   Or visit `/database/run_migration.php` in your browser (admin access required)

3. **Test System**
   ```bash
   php test_unified_system.php
   ```

4. **Verify Installation**
   - Test new user registration
   - Test role selection in dashboard
   - Test admin panel functionality
   - Verify existing users can still access their accounts

## Security Considerations

### Maintained Security Features
- CSRF protection on all forms
- SQL injection prevention
- XSS protection
- Session security
- Input validation and sanitization

### New Security Features
- Enhanced role-based access control
- Audit logging for role changes
- Secure AJAX form submissions

## Browser Compatibility

- **Modern Browsers**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Mobile**: Responsive design works on all mobile devices
- **JavaScript**: Required for AJAX functionality and enhanced UX

## Troubleshooting

### Common Issues

1. **Migration Fails**
   - Check database permissions
   - Ensure backup is created first
   - Review error logs
   - Contact system administrator

2. **Users Can't Access Dashboard**
   - Clear browser cache
   - Check session configuration
   - Verify user account status

3. **Role Selection Not Working**
   - Enable JavaScript in browser
   - Check network connectivity
   - Verify CSRF tokens

4. **Admin Panel Issues**
   - Ensure admin privileges
   - Check database connectivity
   - Verify user_roles table exists

### Support

For technical support or questions:
- Check system logs in `/logs/` directory
- Run the test script: `php test_unified_system.php`
- Contact system administrator

## Future Enhancements

### Planned Features
- Email notifications for role approvals
- Advanced role permissions
- Bulk user management
- Enhanced analytics dashboard
- Mobile app integration

### Customization Options
- Theme customization for role selection cards
- Custom role types
- Configurable approval workflows
- Custom user fields

---

**Version**: 2.0  
**Last Updated**: 2025-01-21  
**Compatibility**: PHP 7.4+, MySQL 5.7+
