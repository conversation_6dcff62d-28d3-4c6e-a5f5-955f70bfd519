<?php
/**
 * Unified Dashboard Notifications Page
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/Notification.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');

$currentUser = getCurrentUser();

// Only allow unified users
if ($currentUser['user_type'] !== 'unified') {
    $redirectUrl = match($currentUser['user_type']) {
        USER_TYPE_ADMIN => '../admin/',
        USER_TYPE_DONOR => '../donor/',
        USER_TYPE_RECIPIENT => '../recipient/',
        default => '../login.php'
    };
    redirectWithMessage($redirectUrl, 'Please use your dedicated dashboard.', 'info');
}

$db = Database::getInstance();

// Handle AJAX requests for marking notifications as read
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    if ($_POST['action'] === 'mark_read' && isset($_POST['notification_id'])) {
        $notificationId = (int)$_POST['notification_id'];
        $success = Notification::markAsRead($currentUser['id'], $notificationId);
        echo json_encode(['success' => $success]);
        exit;
    }
    
    if ($_POST['action'] === 'mark_all_read') {
        $success = Notification::markAllAsRead($currentUser['id']);
        echo json_encode(['success' => $success]);
        exit;
    }
}

// Get filter parameters
$unreadOnly = isset($_GET['unread']) && $_GET['unread'] === '1';
$page = (int)($_GET['page'] ?? 1);

// Get notifications with pagination
$notificationResult = Notification::getUserNotifications($currentUser['id'], $page, RECORDS_PER_PAGE, $unreadOnly);
$notifications = $notificationResult['notifications'];
$pagination = $notificationResult['pagination'];

// Get unread count
$unreadCount = Notification::getUnreadCount($currentUser['id']);

// Get flash message
$flash = getFlashMessage();

$pageTitle = 'My Notifications - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .notification-item {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .notification-item.unread {
            background-color: #f8f9fa;
            border-left-color: #dc3545;
        }
        .notification-item:hover {
            background-color: #e9ecef;
        }
        .mark-read-btn {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .notification-item:hover .mark-read-btn {
            opacity: 1;
        }
        .notification-item.unread .mark-read-btn {
            opacity: 1;
        }
        .unified-navbar {
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg unified-navbar">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="index.php">
                <div class="d-flex align-items-center">
                    <div class="brand-icon">
                        <i class="fas fa-tint"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-title"><?php echo APP_NAME; ?></div>
                        <div class="brand-subtitle">Unified Dashboard</div>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="index.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" href="notifications.php">
                            <i class="fas fa-bell"></i> Notifications
                            <?php if ($unreadCount > 0): ?>
                                <span class="badge bg-warning ms-1"><?php echo $unreadCount; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="../chat/">
                            <i class="fas fa-comments"></i> Messages
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($currentUser['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="add-role.php"><i class="fas fa-plus"></i> Add Role</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <?php if ($flash): ?>
        <div class="container mt-3">
            <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($flash['message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-bell text-danger"></i> My Notifications</h2>
                        <p class="text-muted mb-0">Stay updated with important announcements and system updates</p>
                    </div>
                    <?php if ($unreadCount > 0): ?>
                        <button class="btn btn-outline-primary" onclick="markAllAsRead()">
                            <i class="fas fa-check-double"></i> Mark All as Read
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Filter Options -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="btn-group" role="group">
                                <a href="notifications.php" class="btn btn-<?php echo !$unreadOnly ? 'primary' : 'outline-primary'; ?>">
                                    <i class="fas fa-list"></i> All Notifications
                                </a>
                                <a href="notifications.php?unread=1" class="btn btn-<?php echo $unreadOnly ? 'primary' : 'outline-primary'; ?>">
                                    <i class="fas fa-exclamation-circle"></i> Unread Only
                                    <?php if ($unreadCount > 0): ?>
                                        <span class="badge bg-warning ms-1"><?php echo $unreadCount; ?></span>
                                    <?php endif; ?>
                                </a>
                            </div>
                            <div class="text-muted">
                                <small>
                                    <?php if ($unreadOnly): ?>
                                        Showing <?php echo count($notifications); ?> unread notification(s)
                                    <?php else: ?>
                                        Showing <?php echo count($notifications); ?> notification(s) of <?php echo $pagination['total']; ?> total
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notifications List -->
        <div class="row">
            <div class="col-12">
                <?php if (empty($notifications)): ?>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">
                                <?php echo $unreadOnly ? 'No unread notifications' : 'No notifications found'; ?>
                            </h5>
                            <p class="text-muted">
                                <?php echo $unreadOnly ? 'All caught up! You have no unread notifications.' : 'You haven\'t received any notifications yet.'; ?>
                            </p>
                            <?php if ($unreadOnly): ?>
                                <a href="notifications.php" class="btn btn-primary">
                                    <i class="fas fa-list"></i> View All Notifications
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-body p-0">
                            <?php foreach ($notifications as $index => $notification): ?>
                                <div class="notification-item p-4 <?php echo !$notification['is_read'] ? 'unread' : ''; ?> <?php echo $index < count($notifications) - 1 ? 'border-bottom' : ''; ?>" 
                                     data-notification-id="<?php echo $notification['id']; ?>">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <h6 class="mb-0 me-2"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                                <?php if (!$notification['is_read']): ?>
                                                    <span class="badge bg-danger">New</span>
                                                <?php endif; ?>
                                            </div>
                                            <p class="mb-2 text-muted"><?php echo nl2br(htmlspecialchars($notification['message'])); ?></p>
                                            <div class="d-flex align-items-center text-muted">
                                                <small>
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo formatDate($notification['created_at'], DISPLAY_DATETIME_FORMAT); ?>
                                                </small>
                                                <?php if ($notification['is_read'] && $notification['read_at']): ?>
                                                    <small class="ms-3">
                                                        <i class="fas fa-check me-1"></i>
                                                        Read on <?php echo formatDate($notification['read_at'], DISPLAY_DATETIME_FORMAT); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php if (!$notification['is_read']): ?>
                                            <button class="btn btn-sm btn-outline-primary mark-read-btn" 
                                                    onclick="markAsRead(<?php echo $notification['id']; ?>)"
                                                    title="Mark as read">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                        <div class="d-flex justify-content-center mt-4">
                            <nav>
                                <ul class="pagination">
                                    <?php if ($pagination['current_page'] > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1; ?><?php echo $unreadOnly ? '&unread=1' : ''; ?>">
                                                <i class="fas fa-chevron-left"></i> Previous
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                        <li class="page-item <?php echo $i === $pagination['current_page'] ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo $unreadOnly ? '&unread=1' : ''; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1; ?><?php echo $unreadOnly ? '&unread=1' : ''; ?>">
                                                Next <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        function markAsRead(notificationId) {
            fetch('notifications.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=mark_read&notification_id=${notificationId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`);
                    if (notificationItem) {
                        notificationItem.classList.remove('unread');
                        const newBadge = notificationItem.querySelector('.badge.bg-danger');
                        if (newBadge) newBadge.remove();
                        const markReadBtn = notificationItem.querySelector('.mark-read-btn');
                        if (markReadBtn) markReadBtn.remove();
                    }
                    
                    // Update navbar badge
                    const navBadge = document.querySelector('.navbar .badge');
                    if (navBadge) {
                        const currentCount = parseInt(navBadge.textContent);
                        if (currentCount > 1) {
                            navBadge.textContent = currentCount - 1;
                        } else {
                            navBadge.remove();
                        }
                    }
                    
                    // Update mark all button
                    const markAllBtn = document.querySelector('button[onclick="markAllAsRead()"]');
                    const remainingUnread = document.querySelectorAll('.notification-item.unread').length;
                    if (remainingUnread === 0 && markAllBtn) {
                        markAllBtn.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
                alert('Failed to mark notification as read. Please try again.');
            });
        }

        function markAllAsRead() {
            if (!confirm('Mark all notifications as read?')) return;
            
            fetch('notifications.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=mark_all_read'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
                alert('Failed to mark all notifications as read. Please try again.');
            });
        }
    </script>
</body>
</html>
