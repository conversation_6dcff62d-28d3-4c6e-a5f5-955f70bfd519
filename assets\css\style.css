/**
 * Main Stylesheet
 * Blood Donation Management System
 */

/* Global Styles */
:root {
    --primary-color: #8B0000;
    --primary-dark: #660000;
    --primary-light: #DC143C;
    --blood-red: #8B0000;
    --blood-crimson: #DC143C;
    --blood-maroon: #800000;
    --blood-light: #FFB6C1;
    --secondary-color: #2196f3;
    --secondary-dark: #1976d2;
    --secondary-light: #64b5f6;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #8B0000;
    --info-color: #2196f3;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gray-color: #6c757d;
    --white-color: #ffffff;
    --black-color: #000000;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

/* Blood Theme Animations */
@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.1);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.1);
    }
    70% {
        transform: scale(1);
    }
}

@keyframes bloodDrop {
    0% {
        transform: translateY(-10px);
        opacity: 0.8;
    }
    50% {
        transform: translateY(0px);
        opacity: 1;
    }
    100% {
        transform: translateY(10px);
        opacity: 0.8;
    }
}

/* Blood themed elements */
.blood-gradient {
    background: linear-gradient(135deg, var(--blood-red) 0%, var(--blood-crimson) 50%, var(--blood-maroon) 100%) !important;
}

.blood-shadow {
    box-shadow: 0 6px 20px rgba(139, 0, 0, 0.3);
}

/* Override Bootstrap card backgrounds for blood theme */
.card.blood-theme {
    background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%) !important;
    border: 2px solid #8B0000 !important;
    box-shadow: 0 6px 20px rgba(139, 0, 0, 0.3) !important;
}

.card.blood-theme .card-body {
    background: transparent !important;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(45deg, var(--blood-red), var(--blood-crimson));
    border-color: var(--blood-red);
    box-shadow: 0 2px 8px rgba(139, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(45deg, var(--blood-maroon), var(--blood-red));
    border-color: var(--blood-maroon);
    box-shadow: 0 4px 12px rgba(139, 0, 0, 0.4);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(45deg, var(--blood-red), var(--blood-crimson));
    border-color: var(--blood-red);
    box-shadow: 0 2px 8px rgba(139, 0, 0, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(45deg, var(--blood-maroon), var(--blood-red));
    border-color: var(--blood-maroon);
    box-shadow: 0 4px 12px rgba(139, 0, 0, 0.4);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
}

.btn-danger {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-danger:hover, .btn-danger:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-danger {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-danger:hover, .btn-outline-danger:focus {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Cards */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
    padding: 1rem;
}

.card-footer {
    border-bottom-left-radius: 0.5rem !important;
    border-bottom-right-radius: 0.5rem !important;
    padding: 0.75rem 1rem;
}

.card-header.bg-danger {
    background-color: var(--primary-color) !important;
}

/* Forms */
.form-control:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.25rem rgba(211, 47, 47, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.input-group-text {
    background-color: #f8f9fa;
}

/* Alerts */
.alert {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.alert-danger {
    background-color: #ffebee;
    border-color: #ffcdd2;
    color: #b71c1c;
}

.alert-success {
    background-color: #e8f5e9;
    border-color: #c8e6c9;
    color: #2e7d32;
}

.alert-info {
    background-color: #e3f2fd;
    border-color: #bbdefb;
    color: #0d47a1;
}

.alert-warning {
    background-color: #fff3e0;
    border-color: #ffe0b2;
    color: #e65100;
}

/* Navbar */
.navbar-brand {
    font-weight: 600;
}

.navbar-dark {
    background-color: var(--primary-color);
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 1);
}

.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .nav-link.active {
    color: white;
    font-weight: 600;
}

/* Badges */
.badge-primary {
    background-color: var(--primary-color);
}

.badge-secondary {
    background-color: var(--secondary-color);
}

.badge-success {
    background-color: var(--success-color);
}

.badge-danger {
    background-color: var(--danger-color);
}

.badge-warning {
    background-color: var(--warning-color);
}

.badge-info {
    background-color: var(--info-color);
}

/* Tables */
.table-hover tbody tr:hover {
    background-color: rgba(211, 47, 47, 0.075);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Blood Type Badges */
.blood-type-badge {
    display: inline-block;
    width: 2.5rem;
    height: 2.5rem;
    line-height: 2.5rem;
    text-align: center;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    font-weight: bold;
    margin-right: 0.5rem;
}

/* Urgency Levels */
.urgency-low {
    background-color: #4caf50;
    color: white;
}

.urgency-medium {
    background-color: #ff9800;
    color: white;
}

.urgency-high {
    background-color: #f44336;
    color: white;
}

.urgency-critical {
    background-color: #b71c1c;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

/* Status Badges */
.status-pending {
    background-color: #ff9800;
    color: white;
}

.status-approved {
    background-color: #2196f3;
    color: white;
}

.status-fulfilled {
    background-color: #4caf50;
    color: white;
}

.status-cancelled {
    background-color: #9e9e9e;
    color: white;
}

.status-scheduled {
    background-color: #2196f3;
    color: white;
}

.status-completed {
    background-color: #4caf50;
    color: white;
}

/* Dashboard Cards */
.dashboard-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(139, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(139, 0, 0, 0.1);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(139, 0, 0, 0.2);
    border-color: var(--primary-color);
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

.dashboard-card .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Admin Dashboard Specific Styles */
.admin-dashboard .welcome-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border: none;
    color: white;
}

.admin-dashboard .stats-card {
    border-left: 4px solid var(--primary-color);
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
}

.admin-dashboard .stats-card .card-icon {
    color: var(--primary-color);
    font-size: 2.5rem;
    opacity: 0.8;
}

.admin-dashboard .stats-card .card-text {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 2rem;
}

.admin-dashboard .activity-card {
    border-top: 3px solid var(--primary-color);
}

.admin-dashboard .inventory-card {
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
    border: 1px solid rgba(139, 0, 0, 0.1);
}

.dashboard-card .card-text {
    font-size: 2rem;
    font-weight: 700;
}

.dashboard-card .card-icon {
    font-size: 3rem;
    opacity: 0.2;
    position: absolute;
    right: 1rem;
    bottom: 1rem;
}

/* Chat Styles */
.chat-container {
    height: 400px;
    overflow-y: auto;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
}

.chat-message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    max-width: 75%;
}

.chat-message-sender {
    background-color: #e3f2fd;
    margin-left: auto;
    border-top-right-radius: 0;
}

.chat-message-receiver {
    background-color: white;
    margin-right: auto;
    border-top-left-radius: 0;
}

.chat-time {
    font-size: 0.75rem;
    color: #6c757d;
    text-align: right;
    margin-top: 0.25rem;
}

/* Notification Styles */
.notification-item {
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Footer */
.footer {
    background-color: #343a40;
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer a {
    color: rgba(255, 255, 255, 0.8);
}

.footer a:hover {
    color: white;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-header h4 {
        font-size: 1.25rem;
    }
    
    .dashboard-card .card-text {
        font-size: 1.5rem;
    }
    
    .dashboard-card .card-icon {
        font-size: 2rem;
    }
    
    .chat-message {
        max-width: 85%;
    }
}

@media (max-width: 576px) {
    .card-header h4 {
        font-size: 1.1rem;
    }
    
    .dashboard-card .card-text {
        font-size: 1.25rem;
    }
    
    .dashboard-card .card-icon {
        font-size: 1.5rem;
    }
    
    .chat-message {
        max-width: 90%;
    }
}

/* Fix gradient card visibility */
.card.bg-gradient {
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5); /* Add text shadow for better readability */
}
.card.bg-gradient .card-body {
    background: rgba(0, 0, 0, 0.1) !important; /* Semi-transparent dark overlay for better contrast */
    color: inherit;
}

/* User Status Indicators */
.user-avatar-container {
    position: relative;
    display: inline-block;
}

.user-status-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #ffffff;
    z-index: 10;
}

.user-status-indicator.active {
    background-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

.user-status-indicator.inactive {
    background-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(139, 0, 0, 0.3);
    animation: pulse-red 2s infinite;
}

@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 2px rgba(139, 0, 0, 0.3);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(139, 0, 0, 0.1);
    }
    100% {
        box-shadow: 0 0 0 2px rgba(139, 0, 0, 0.3);
    }
}

/* Status Toggle Buttons */
.status-toggle {
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.status-toggle.active {
    background-color: #28a745;
    color: white;
}

.status-toggle.active:hover {
    background-color: #218838;
    transform: scale(1.05);
}

.status-toggle.inactive {
    background-color: var(--primary-color);
    color: white;
}

.status-toggle.inactive:hover {
    background-color: var(--primary-dark);
    transform: scale(1.05);
}

/* Admin Panel Enhancements */
.admin-panel-section {
    background: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(139, 0, 0, 0.1);
    border-left: 4px solid var(--primary-color);
    margin-bottom: 1.5rem;
}

.admin-panel-section .section-header {
    background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
    border-bottom: 1px solid rgba(139, 0, 0, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
}

.admin-panel-section .section-header h5 {
    color: var(--primary-color);
    margin: 0;
    font-weight: 600;
}

.admin-panel-section .section-body {
    padding: 1.5rem;
}

/* Dashboard Analytics Enhancements */
.stats-card .card-text {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 0 1px 3px rgba(139, 0, 0, 0.1);
    animation: countUp 1s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card:hover .card-text {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

/* Blood Type Badges */
.blood-type-badge {
    display: inline-block;
    width: 50px;
    height: 50px;
    line-height: 50px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border-radius: 50%;
    font-weight: bold;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 8px rgba(139, 0, 0, 0.3);
}

/* Activity Timeline */
.activity-timeline {
    position: relative;
    padding-left: 2rem;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
}

.activity-item {
    position: relative;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(139, 0, 0, 0.1);
    border-left: 3px solid var(--primary-color);
}

.activity-item::before {
    content: '';
    position: absolute;
    left: -2.25rem;
    top: 1.25rem;
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    border: 3px solid #ffffff;
    box-shadow: 0 0 0 2px var(--primary-color);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .admin-panel-section {
        margin-bottom: 1rem;
    }

    .stats-card .card-text {
        font-size: 2rem;
    }

    .blood-type-badge {
        width: 40px;
        height: 40px;
        line-height: 40px;
        font-size: 1rem;
    }

    .activity-timeline {
        padding-left: 1rem;
    }

    .activity-item::before {
        left: -1.5rem;
    }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(139, 0, 0, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Heartbeat Animation for Donation Icons */
@keyframes heartbeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

/* Enhanced Admin Dashboard Styling */
.admin-welcome-card {
    background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%);
    border: none;
    border-radius: 1rem;
    box-shadow: 0 8px 32px rgba(139, 0, 0, 0.3);
    overflow: hidden;
}

.admin-welcome-card .card-body {
    position: relative;
    z-index: 2;
}

.admin-welcome-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

/* Enhanced Statistics Cards */
.stats-card-enhanced {
    transition: all 0.3s ease;
    border: none;
    border-radius: 0.75rem;
    overflow: hidden;
}

.stats-card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(139, 0, 0, 0.15);
}

.stats-card-enhanced .card-body {
    position: relative;
    padding: 1.5rem;
}

.stats-card-enhanced .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
}

.stats-card-enhanced .stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

/* Quick Action Cards */
.quick-action-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 0.75rem;
    overflow: hidden;
}

.quick-action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(139, 0, 0, 0.15);
}

.quick-action-card .card-header {
    background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%);
    border: none;
    padding: 1rem 1.25rem;
}

.quick-action-card .btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.quick-action-card .btn:hover {
    transform: translateX(3px);
}

/* Improved spacing and typography */
.container-fluid {
    padding-left: 2rem;
    padding-right: 2rem;
}

@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .admin-welcome-card .card-body {
        padding: 2rem 1.5rem;
    }

    .stats-card-enhanced .stats-number {
        font-size: 2rem;
    }
}

/* Fix for any layout issues */
.row.g-4 > * {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.card.h-100 {
    height: 100% !important;
}

/* Ensure proper text contrast */
.text-danger {
    color: #8B0000 !important;
}

.bg-danger {
    background-color: #8B0000 !important;
}

.btn-danger {
    background-color: #8B0000;
    border-color: #8B0000;
}

.btn-danger:hover {
    background-color: #660000;
    border-color: #660000;
}

.btn-outline-danger {
    color: #8B0000;
    border-color: #8B0000;
}

.btn-outline-danger:hover {
    background-color: #8B0000;
    border-color: #8B0000;
}

/* Enhanced Admin Navigation Bar */
.admin-navbar {
    background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%);
    box-shadow: 0 2px 20px rgba(139, 0, 0, 0.3);
    border: none;
    padding: 0.5rem 0;
}

.admin-navbar .navbar-brand {
    color: white !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.admin-navbar .navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-brand-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.brand-title {
    font-size: 1.1rem;
    font-weight: 700;
    line-height: 1.2;
    margin: 0;
    text-align: center;
}

.brand-subtitle {
    font-size: 0.75rem;
    opacity: 0.8;
    line-height: 1;
    text-align: center;
}

/* Navigation Links */
.admin-navbar .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.admin-navbar .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white !important;
    transform: translateY(-1px);
}

.admin-navbar .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.nav-icon {
    font-size: 1rem;
    margin-right: 0.5rem;
    width: 16px;
    text-align: center;
}

.nav-text {
    font-size: 0.9rem;
}

/* Admin Profile */
.admin-profile {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    margin-left: 1rem;
}

.admin-profile:hover {
    background: rgba(255, 255, 255, 0.2);
}

.admin-avatar {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.admin-avatar-large {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin: 0 auto;
}

.admin-name {
    font-size: 0.85rem;
    font-weight: 600;
    line-height: 1.2;
    color: white;
}

.admin-role {
    font-size: 0.7rem;
    opacity: 0.8;
    line-height: 1;
    color: rgba(255, 255, 255, 0.8);
}

.admin-info {
    text-align: left;
}

/* Enhanced Dropdown */
.admin-dropdown {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 10px 40px rgba(139, 0, 0, 0.2);
    padding: 0.5rem 0;
    min-width: 250px;
}

.admin-dropdown .dropdown-header {
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 0.75rem 0.75rem 0 0;
    margin: -0.5rem -0.5rem 0.5rem -0.5rem;
}

.admin-dropdown .dropdown-item {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 0.5rem;
    margin: 0 0.5rem;
}

.admin-dropdown .dropdown-item:hover {
    background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%);
    color: white;
    transform: translateX(5px);
}

.admin-dropdown .dropdown-item.text-danger:hover {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

/* Mobile Navigation */
@media (max-width: 991.98px) {
    .admin-navbar .navbar-collapse {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        margin-top: 1rem;
        padding: 1rem;
    }

    .admin-navbar .nav-link {
        margin: 0.25rem 0;
    }

    .admin-profile {
        margin-left: 0;
        margin-top: 1rem;
    }

    .brand-title {
        font-size: 1rem;
    }

    .brand-subtitle {
        display: none;
    }
}

/* Navbar Toggler */
.admin-navbar .navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.5rem;
    padding: 0.5rem;
}

.admin-navbar .navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.admin-navbar .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.card.bg-gradient h2,
.card.bg-gradient h3,
.card.bg-gradient h4,
.card.bg-gradient h5,
.card.bg-gradient p {
    color: #ffffff;
    font-weight: 500; /* Slightly bolder text */
    letter-spacing: 0.01em; /* Slightly increased letter spacing */
}
.card.bg-gradient .alert {
    border: 1px solid rgba(255, 255, 255, 0.3); /* Add subtle border to alerts within cards */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Add subtle shadow */
}
