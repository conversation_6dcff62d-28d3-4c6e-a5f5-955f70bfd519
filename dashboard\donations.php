<?php
/**
 * My Donations - Unified Dashboard
 * Blood Donation Management System
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../classes/UnifiedUser.php';

// Start session and check authentication
startSecureSession();
requireUnifiedAccess('../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();
$unifiedUser = new UnifiedUser($currentUser['id']);

// Get current active role from session
$currentRole = $_SESSION['current_role'] ?? $unifiedUser->getPrimaryRole();

// Check if user has donor role and if donor is the current active role
if (!$unifiedUser->hasRole('donor')) {
    redirectWithMessage('index.php', 'You need donor role to access this page.', 'error');
}

if ($currentRole !== 'donor') {
    redirectWithMessage('index.php', 'Only donors can access donation features. Please switch to donor mode to access this feature.', 'error');
}

// Get user's donations
$donations = $db->fetchAll("
    SELECT d.*, bt.type as blood_type
    FROM donations d
    LEFT JOIN blood_types bt ON d.blood_type_id = bt.id
    WHERE d.donor_id = ?
    ORDER BY d.donation_date DESC
", [$currentUser['id']]);

$pageTitle = 'My Donations';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(90deg, #8B0000 0%, #DC143C 100%);">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-heart"></i> My Donation History</h2>
                    <div>
                        <a href="schedule.php" class="btn btn-outline-primary me-2">
                            <i class="fas fa-calendar"></i> View Schedule
                        </a>
                        <span class="badge bg-success fs-6">
                            Total Donations: <?php echo count($donations); ?>
                        </span>
                    </div>
                </div>

                <?php if (empty($donations)): ?>
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> No Donations Yet</h5>
                        <p>You haven't made any donations yet. Thank you for your willingness to help save lives!</p>
                        <p>Donations are scheduled by administrators. Please contact them to schedule your first donation.</p>
                        <a href="../chat/find-users-restricted.php" class="btn btn-primary">
                            <i class="fas fa-comments"></i> Contact Administrators
                        </a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($donations as $donation): ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">Donation #<?php echo $donation['id']; ?></h6>
                                        <span class="badge bg-<?php 
                                            echo $donation['status'] === 'completed' ? 'success' : 
                                                ($donation['status'] === 'scheduled' ? 'primary' : 
                                                ($donation['status'] === 'cancelled' ? 'secondary' : 'warning')); 
                                        ?>">
                                            <?php echo ucfirst($donation['status']); ?>
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Date:</strong> <?php echo formatDate($donation['donation_date']); ?></p>
                                        <p><strong>Blood Type:</strong> <?php echo htmlspecialchars($donation['blood_type'] ?? 'N/A'); ?></p>
                                        <p><strong>Units:</strong> <?php echo $donation['units_donated'] ?? 'N/A'; ?></p>
                                        <?php if ($donation['location']): ?>
                                            <p><strong>Location:</strong> <?php echo htmlspecialchars($donation['location']); ?></p>
                                        <?php endif; ?>
                                        <?php if ($donation['notes']): ?>
                                            <p><strong>Notes:</strong> <?php echo htmlspecialchars($donation['notes']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-footer">
                                        <small class="text-muted">
                                            <?php if ($donation['status'] === 'completed'): ?>
                                                <i class="fas fa-check-circle text-success"></i> Thank you for your donation!
                                            <?php elseif ($donation['status'] === 'scheduled'): ?>
                                                <i class="fas fa-clock text-primary"></i> Upcoming donation
                                            <?php else: ?>
                                                <i class="fas fa-info-circle"></i> Status: <?php echo ucfirst($donation['status']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Donation Statistics -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-bar"></i> Donation Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <div class="stat-item">
                                                <h3 class="text-success"><?php echo count($donations); ?></h3>
                                                <p class="text-muted">Total Donations</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="stat-item">
                                                <h3 class="text-primary">
                                                    <?php
                                                    $totalUnits = array_sum(array_column($donations, 'units_donated'));
                                                    echo $totalUnits ?: '0';
                                                    ?>
                                                </h3>
                                                <p class="text-muted">Units Donated</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="stat-item">
                                                <h3 class="text-info">
                                                    <?php 
                                                    $completedDonations = array_filter($donations, function($d) { 
                                                        return $d['status'] === 'completed'; 
                                                    });
                                                    echo count($completedDonations);
                                                    ?>
                                                </h3>
                                                <p class="text-muted">Completed</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="stat-item">
                                                <h3 class="text-warning">
                                                    <?php 
                                                    $scheduledDonations = array_filter($donations, function($d) { 
                                                        return $d['status'] === 'scheduled'; 
                                                    });
                                                    echo count($scheduledDonations);
                                                    ?>
                                                </h3>
                                                <p class="text-muted">Scheduled</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .stat-item {
            padding: 1rem;
        }
        .stat-item h3 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
    </style>
</body>
</html>
