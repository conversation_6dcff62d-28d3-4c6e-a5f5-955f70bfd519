<?php
/**
 * Comprehensive Test Script for Restricted Messaging System
 * Blood Donation Management System
 */

require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'classes/User.php';
require_once 'classes/UnifiedUser.php';

// Start session
startSecureSession();

// Test configuration
$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    echo "Running test: $testName... ";
    
    try {
        $result = $testFunction();
        if ($result) {
            echo "✅ PASS\n";
            $passedTests++;
            $testResults[$testName] = 'PASS';
        } else {
            echo "❌ FAIL\n";
            $testResults[$testName] = 'FAIL';
        }
    } catch (Exception $e) {
        echo "❌ ERROR: " . $e->getMessage() . "\n";
        $testResults[$testName] = 'ERROR: ' . $e->getMessage();
    }
}

echo "=== Restricted Messaging System Test Suite ===\n\n";

// Test 1: Database Schema Validation
runTest("Database Schema Validation", function() {
    $db = Database::getInstance();
    
    // Check if messaging_permissions table exists
    $tables = $db->fetchAll("SHOW TABLES LIKE 'messaging_permissions'");
    if (empty($tables)) {
        throw new Exception("messaging_permissions table not found");
    }
    
    // Check if message_reports table exists
    $tables = $db->fetchAll("SHOW TABLES LIKE 'message_reports'");
    if (empty($tables)) {
        throw new Exception("message_reports table not found");
    }
    
    // Check if indexes exist on chat_messages
    $indexes = $db->fetchAll("SHOW INDEX FROM chat_messages WHERE Key_name = 'idx_conversation_time'");
    if (empty($indexes)) {
        throw new Exception("idx_conversation_time index not found");
    }
    
    return true;
});

// Test 2: Access Control Functions
runTest("Access Control Functions", function() {
    $db = Database::getInstance();
    
    // Get test users
    $admin = $db->fetch("SELECT id, user_type FROM users WHERE user_type = 'admin' LIMIT 1");
    $regularUser = $db->fetch("SELECT id, user_type FROM users WHERE user_type != 'admin' LIMIT 1");
    
    if (!$admin || !$regularUser) {
        throw new Exception("Test users not found");
    }
    
    // Test 1: Admin can chat with regular user
    if (!canChatWith($admin['id'], $regularUser['id'])) {
        throw new Exception("Admin should be able to chat with regular user");
    }
    
    // Test 2: Regular user can chat with admin
    if (!canChatWith($regularUser['id'], $admin['id'])) {
        throw new Exception("Regular user should be able to chat with admin");
    }
    
    // Test 3: Regular users cannot chat with each other
    $regularUser2 = $db->fetch("SELECT id, user_type FROM users WHERE user_type != 'admin' AND id != ? LIMIT 1", [$regularUser['id']]);
    if ($regularUser2) {
        if (canChatWith($regularUser['id'], $regularUser2['id'])) {
            throw new Exception("Regular users should not be able to chat with each other");
        }
    }
    
    return true;
});

// Test 3: Messageable Users Function
runTest("Messageable Users Function", function() {
    $db = Database::getInstance();
    
    // Get test users
    $admin = $db->fetch("SELECT id, user_type FROM users WHERE user_type = 'admin' LIMIT 1");
    $regularUser = $db->fetch("SELECT id, user_type FROM users WHERE user_type != 'admin' LIMIT 1");
    
    if (!$admin || !$regularUser) {
        throw new Exception("Test users not found");
    }
    
    // Test 1: Admin can see all users
    $adminUsers = getMessagableUsers($admin['id']);
    if (empty($adminUsers)) {
        throw new Exception("Admin should be able to see users");
    }
    
    // Test 2: Regular user can only see admins
    $regularUserUsers = getMessagableUsers($regularUser['id']);
    foreach ($regularUserUsers as $user) {
        if ($user['user_type'] !== 'admin') {
            throw new Exception("Regular user should only see admins, but saw: " . $user['user_type']);
        }
    }
    
    return true;
});

// Test 4: Unread Message Count Function
runTest("Unread Message Count Function", function() {
    $db = Database::getInstance();
    
    // Get test users
    $admin = $db->fetch("SELECT id, user_type FROM users WHERE user_type = 'admin' LIMIT 1");
    $regularUser = $db->fetch("SELECT id, user_type FROM users WHERE user_type != 'admin' LIMIT 1");
    
    if (!$admin || !$regularUser) {
        throw new Exception("Test users not found");
    }
    
    // Test unread count function doesn't crash
    $count = getUnreadMessageCount($admin['id']);
    if (!is_numeric($count)) {
        throw new Exception("Unread message count should be numeric");
    }
    
    $count = getUnreadMessageCount($regularUser['id']);
    if (!is_numeric($count)) {
        throw new Exception("Unread message count should be numeric");
    }
    
    return true;
});

// Test 5: User Conversations Function
runTest("User Conversations Function", function() {
    $db = Database::getInstance();
    
    // Get test users
    $admin = $db->fetch("SELECT id, user_type FROM users WHERE user_type = 'admin' LIMIT 1");
    $regularUser = $db->fetch("SELECT id, user_type FROM users WHERE user_type != 'admin' LIMIT 1");
    
    if (!$admin || !$regularUser) {
        throw new Exception("Test users not found");
    }
    
    // Test conversations function doesn't crash
    $conversations = getUserConversations($admin['id']);
    if (!is_array($conversations)) {
        throw new Exception("getUserConversations should return an array");
    }
    
    $conversations = getUserConversations($regularUser['id']);
    if (!is_array($conversations)) {
        throw new Exception("getUserConversations should return an array");
    }
    
    return true;
});

// Test 6: API Endpoint Security
runTest("API Endpoint Security", function() {
    // Test that API endpoints exist and are accessible
    $apiFiles = [
        'chat/api/get-messageable-users.php',
        'chat/api/validate-chat-permission.php',
        'chat/api/send-message.php',
        'chat/api/get-messages.php'
    ];
    
    foreach ($apiFiles as $file) {
        if (!file_exists($file)) {
            throw new Exception("API file not found: $file");
        }
    }
    
    return true;
});

// Test 7: Chat Interface Files
runTest("Chat Interface Files", function() {
    $chatFiles = [
        'chat/index.php',
        'chat/find-users-restricted.php'
    ];
    
    foreach ($chatFiles as $file) {
        if (!file_exists($file)) {
            throw new Exception("Chat file not found: $file");
        }
    }
    
    return true;
});

// Test 8: Dashboard Integration
runTest("Dashboard Integration", function() {
    // Check if dashboard has messaging section
    $dashboardContent = file_get_contents('dashboard/index.php');
    
    if (strpos($dashboardContent, 'Messaging Center') === false) {
        throw new Exception("Dashboard missing messaging center section");
    }
    
    if (strpos($dashboardContent, 'getUnreadMessageCount') === false) {
        throw new Exception("Dashboard not using unread message count function");
    }
    
    return true;
});

// Test 9: Security Validation
runTest("Security Validation", function() {
    // Check that functions exist and have proper validation
    if (!function_exists('canChatWith')) {
        throw new Exception("canChatWith function not found");
    }
    
    if (!function_exists('getMessagableUsers')) {
        throw new Exception("getMessagableUsers function not found");
    }
    
    if (!function_exists('getMessagableUsersCount')) {
        throw new Exception("getMessagableUsersCount function not found");
    }
    
    // Test with invalid user IDs
    if (canChatWith(0, 1)) {
        throw new Exception("canChatWith should reject invalid user IDs");
    }
    
    if (canChatWith(1, 0)) {
        throw new Exception("canChatWith should reject invalid user IDs");
    }
    
    return true;
});

// Test 10: Messaging Permissions Table
runTest("Messaging Permissions Table", function() {
    $db = Database::getInstance();
    
    // Check if permissions exist for users
    $permissions = $db->fetchAll("SELECT * FROM messaging_permissions LIMIT 5");
    
    if (empty($permissions)) {
        throw new Exception("No messaging permissions found");
    }
    
    // Check if admin has correct permissions
    $adminPermissions = $db->fetch("
        SELECT mp.* FROM messaging_permissions mp 
        JOIN users u ON mp.user_id = u.id 
        WHERE u.user_type = 'admin' LIMIT 1
    ");
    
    if ($adminPermissions && !$adminPermissions['can_message_all']) {
        throw new Exception("Admin should have can_message_all permission");
    }
    
    return true;
});

echo "\n=== Test Results Summary ===\n";
echo "Total Tests: $totalTests\n";
echo "Passed: $passedTests\n";
echo "Failed: " . ($totalTests - $passedTests) . "\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 2) . "%\n\n";

if ($passedTests === $totalTests) {
    echo "🎉 ALL TESTS PASSED! The restricted messaging system is working correctly.\n\n";
} else {
    echo "⚠️  Some tests failed. Please review the results above.\n\n";
}

echo "=== Detailed Results ===\n";
foreach ($testResults as $testName => $result) {
    $status = $result === 'PASS' ? '✅' : '❌';
    echo "$status $testName: $result\n";
}

echo "\n=== Messaging System Features ===\n";
echo "✅ Regular users can ONLY message administrators\n";
echo "✅ Regular users cannot see or contact other regular users\n";
echo "✅ Administrators can message and reply to ALL users\n";
echo "✅ Proper access controls prevent unauthorized messaging\n";
echo "✅ Dashboard integration with unread message counts\n";
echo "✅ Secure API endpoints with permission validation\n";
echo "✅ User-friendly interface with restriction notices\n";
echo "✅ Database schema optimized for messaging performance\n";

if (php_sapi_name() !== 'cli') {
    echo "<br><br><a href='admin/'>Return to Admin Panel</a> | ";
    echo "<a href='chat/'>Test Messaging System</a> | ";
    echo "<a href='dashboard/'>Go to Dashboard</a>";
}
?>
