<?php
/**
 * Run Email Removal Migration
 * Blood Donation Management System
 * 
 * This script runs the migration to remove email functionality from the system.
 */

echo "Starting Email Removal Migration...\n";
echo "This will completely remove email functionality from the system.\n";
echo "Users will only be able to login with their username.\n\n";

echo "Do you want to continue? (y/n): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
$continue = trim(strtolower($line));

if ($continue !== 'y' && $continue !== 'yes') {
    echo "Migration cancelled.\n";
    exit(0);
}

// Run the migration
require_once 'database/migration_remove_email.php';

echo "\nMigration completed!\n";
echo "Please test the system to ensure everything works correctly.\n";
?> 